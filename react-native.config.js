// Import Metro commands from the new React Native CLI plugin
let commands = [];

try {
  const { bundleCommand, startCommand } = require('@react-native/community-cli-plugin');
  commands = [bundleCommand, startCommand];
} catch (error) {
  console.warn('Warning: @react-native/community-cli-plugin not found, Metro commands may not be available');
}

// Import QRN commands
try {
  const qrnConfig = require('@qnpm/QRNPackager/react-native.config.js');
  if (qrnConfig.commands) {
    commands = commands.concat(qrnConfig.commands);
  }
} catch (error) {
  console.warn('Warning: QRN commands not found, QRN bundle command may not be available');
}

module.exports = {
  commands,
};
