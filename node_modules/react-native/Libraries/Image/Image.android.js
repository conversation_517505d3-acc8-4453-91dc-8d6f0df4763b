/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 */

import type {ImageStyleProp} from '../StyleSheet/StyleSheet';
import type {RootTag} from '../Types/RootTagTypes';
import type {AbstractImageAndroid, ImageAndroid} from './ImageTypes.flow';

import flattenStyle from '../StyleSheet/flattenStyle';
import StyleSheet from '../StyleSheet/StyleSheet';
import TextAncestor from '../Text/TextAncestor';
import ImageAnalyticsTagContext from './ImageAnalyticsTagContext';
import {
  unstable_getImageComponentDecorator,
  useWrapRefWithImageAttachedCallbacks,
} from './ImageInjection';
import {getImageSourcesFromImageProps} from './ImageSourceUtils';
import {convertObjectFitToResizeMode} from './ImageUtils';
import ImageViewNativeComponent from './ImageViewNativeComponent';
import NativeImageLoaderAndroid, {
  type ImageSize,
} from './NativeImageLoaderAndroid';
import resolveAssetSource from './resolveAssetSource';
import TextInlineImageNativeComponent from './TextInlineImageNativeComponent';
import * as React from 'react';

let _requestId = 1;
function generateRequestId() {
  return _requestId++;
}

// QRN ADD
const PropTypes = require('prop-types');
const DeprecatedImageStylePropTypes = require('deprecated-react-native-prop-types/DeprecatedImageStylePropTypes');
const DeprecatedStyleSheetPropType = require('deprecated-react-native-prop-types/DeprecatedStyleSheetPropType');
const DeprecatedViewPropTypes = require('deprecated-react-native-prop-types/DeprecatedViewPropTypes');

const ImageProps = {
    ...DeprecatedViewPropTypes,
    style: (DeprecatedStyleSheetPropType(DeprecatedImageStylePropTypes): ReactPropsCheckType),
    /**
     * See https://reactnative.dev/docs/image.html#source
     */
    source: (PropTypes.oneOfType([
        PropTypes.shape({
            uri: PropTypes.string,
            headers: PropTypes.objectOf(PropTypes.string),
        }),
        // Opaque type returned by require('./image.jpg')
        PropTypes.number,
        // Multiple sources
        PropTypes.arrayOf(
            PropTypes.shape({
                uri: PropTypes.string,
                width: PropTypes.number,
                height: PropTypes.number,
                headers: PropTypes.objectOf(PropTypes.string),
            })
        ),
    ]): React$PropType$Primitive<
        | {
              headers?: { [string]: string, ... },
              uri?: string,
              ...
          }
        | number
        | Array<{
              headers?: { [string]: string, ... },
              height?: number,
              uri?: string,
              width?: number,
              ...
          }>
    >),
    /**
     * blurRadius: the blur radius of the blur filter added to the image
     *
     * See https://reactnative.dev/docs/image.html#blurradius
     */
    blurRadius: PropTypes.number,
    /** QRN change defaultSource
     * See https://reactnative.dev/docs/image.html#defaultsource
     */
    defaultSource: PropTypes.oneOfType([
        PropTypes.shape({
            uri: PropTypes.string,
            width: PropTypes.number,
            height: PropTypes.number,
            scale: PropTypes.number,
        }),
        PropTypes.number,
    ]),
    /**
     * Animated webp play or display first frame.
     */
    animated: PropTypes.bool,
    /** QRN END**/
    /**
     * See https://reactnative.dev/docs/image.html#loadingindicatorsource
     */
    loadingIndicatorSource: (PropTypes.oneOfType([
        PropTypes.shape({
            uri: PropTypes.string,
        }),
        // Opaque type returned by require('./image.jpg')
        PropTypes.number,
    ]): React$PropType$Primitive<{ uri?: string, ... } | number>),
    progressiveRenderingEnabled: PropTypes.bool,
    fadeDuration: PropTypes.number,
    /**
     * Analytics Tag used by this Image
     */
    internal_analyticTag: PropTypes.string,
    /**
     * Invoked on load start
     */
    onLoadStart: PropTypes.func,
    /**
     * Invoked on load error
     */
    onError: PropTypes.func,
    /**
     * Invoked when load completes successfully
     */
    onLoad: PropTypes.func,
    /**
     * Invoked when load either succeeds or fails
     */
    onLoadEnd: PropTypes.func,
    /**
     * Used to locate this view in end-to-end tests.
     */
    testID: PropTypes.string,
    /**
     * The mechanism that should be used to resize the image when the image's dimensions
     * differ from the image view's dimensions. Defaults to `auto`.
     *
     * See https://reactnative.dev/docs/image.html#resizemethod
     */
    resizeMethod: (PropTypes.oneOf(['auto', 'resize', 'scale']): React$PropType$Primitive<'auto' | 'resize' | 'scale'>),
    /**
     * Determines how to resize the image when the frame doesn't match the raw
     * image dimensions.
     *
     * See https://reactnative.dev/docs/image.html#resizemode
     */
    resizeMode: (PropTypes.oneOf(['cover', 'contain', 'stretch', 'repeat', 'center']): React$PropType$Primitive<
        'cover' | 'contain' | 'stretch' | 'repeat' | 'center'
    >),
};
// QRN END

/**
 * Retrieve the width and height (in pixels) of an image prior to displaying it
 *
 * See https://reactnative.dev/docs/image#getsize
 */
function getSize(
  url: string,
  success?: (width: number, height: number) => void,
  failure?: (error: mixed) => void,
): void | Promise<ImageSize> {
  const promise = NativeImageLoaderAndroid.getSize(url);
  if (typeof success !== 'function') {
    return promise;
  }
  promise
    .then(sizes => success(sizes.width, sizes.height))
    .catch(
      failure ||
        function () {
          console.warn('Failed to get size for image: ' + url);
        },
    );
}

/**
 * Retrieve the width and height (in pixels) of an image prior to displaying it
 * with the ability to provide the headers for the request
 *
 * See https://reactnative.dev/docs/image#getsizewithheaders
 */
function getSizeWithHeaders(
  url: string,
  headers: {[string]: string, ...},
  success?: (width: number, height: number) => void,
  failure?: (error: mixed) => void,
): void | Promise<ImageSize> {
  const promise = NativeImageLoaderAndroid.getSizeWithHeaders(url, headers);
  if (typeof success !== 'function') {
    return promise;
  }
  promise
    .then(sizes => success(sizes.width, sizes.height))
    .catch(
      failure ||
        function () {
          console.warn('Failed to get size for image: ' + url);
        },
    );
}

function prefetchWithMetadata(
  url: string,
  queryRootName: string,
  rootTag?: ?RootTag,
  callback: ?(requestId: number) => void,
): Promise<boolean> {
  // TODO: T79192300 Log queryRootName and rootTag
  return prefetch(url, callback);
}

function prefetch(
  url: string,
  callback: ?(requestId: number) => void,
): Promise<boolean> {
  const requestId = generateRequestId();
  callback && callback(requestId);
  return NativeImageLoaderAndroid.prefetchImage(url, requestId);
}

function abortPrefetch(requestId: number): void {
  NativeImageLoaderAndroid.abortRequest(requestId);
}

/**
 * Perform cache interrogation.
 *
 * See https://reactnative.dev/docs/image#querycache
 */
async function queryCache(
  urls: Array<string>,
): Promise<{[string]: 'memory' | 'disk' | 'disk/memory', ...}> {
  return NativeImageLoaderAndroid.queryCache(urls);
}

/**
 * A React component for displaying different types of images,
 * including network images, static resources, temporary local images, and
 * images from local disk, such as the camera roll.
 *
 * See https://reactnative.dev/docs/image
 */
let BaseImage: AbstractImageAndroid = React.forwardRef(
  (props, forwardedRef) => {
    let source = getImageSourcesFromImageProps(props) || {
      uri: undefined,
      width: undefined,
      height: undefined,
    };
    const loadingIndicatorSource = resolveAssetSource(
      props.loadingIndicatorSource,
    );

    if (props.children) {
      throw new Error(
        'The <Image> component cannot contain children. If you want to render content on top of the image, consider using the <ImageBackground> component or absolute positioning.',
      );
    }

    if (props.defaultSource != null && props.loadingIndicatorSource != null) {
      throw new Error(
        'The <Image> component cannot have defaultSource and loadingIndicatorSource at the same time. Please use either defaultSource or loadingIndicatorSource.',
      );
    }

    let style: ImageStyleProp;
    let sources;
    if (Array.isArray(source)) {
      style = [styles.base, props.style];
      sources = source;
    } else {
      const {uri} = source;
      if (uri === '') {
        console.warn('source.uri should not be an empty string');
      }
      const width = source.width ?? props.width;
      const height = source.height ?? props.height;
      style = [{width, height}, styles.base, props.style];
      sources = [source];
    }

    const {onLoadStart, onLoad, onLoadEnd, onError} = props;
    const nativeProps = {
      ...props,
      style,
      shouldNotifyLoadEvents: !!(onLoadStart || onLoad || onLoadEnd || onError),
      // Both iOS and C++ sides expect to have "source" prop, whereas on Android it's "src"
      // (for historical reasons). So in the latter case we populate both "src" and "source",
      // in order to have a better alignment between platforms in the future.
      src: sources,
      source: sources,
      /* $FlowFixMe(>=0.78.0 site=react_native_android_fb) This issue was found
       * when making Flow check .android.js files. */
      headers: (source?.[0]?.headers || source?.headers: ?{[string]: string}),
      loadingIndicatorSrc: loadingIndicatorSource
        ? loadingIndicatorSource.uri
        : null,
      accessibilityLabel:
        props['aria-label'] ?? props.accessibilityLabel ?? props.alt,
      accessibilityLabelledBy:
        props?.['aria-labelledby'] ?? props?.accessibilityLabelledBy,
      accessible: props.alt !== undefined ? true : props.accessible,
      accessibilityState: {
        busy: props['aria-busy'] ?? props.accessibilityState?.busy,
        checked: props['aria-checked'] ?? props.accessibilityState?.checked,
        disabled: props['aria-disabled'] ?? props.accessibilityState?.disabled,
        expanded: props['aria-expanded'] ?? props.accessibilityState?.expanded,
        selected: props['aria-selected'] ?? props.accessibilityState?.selected,
      },
    };

    const flattenedStyle = flattenStyle<ImageStyleProp>(style);
    const objectFit = convertObjectFitToResizeMode(flattenedStyle?.objectFit);
    const resizeMode =
      objectFit || props.resizeMode || flattenedStyle?.resizeMode || 'cover';

    const actualRef = useWrapRefWithImageAttachedCallbacks(forwardedRef);

    return (
      <ImageAnalyticsTagContext.Consumer>
        {analyticTag => {
          const nativePropsWithAnalytics =
            analyticTag !== null
              ? {
                  ...nativeProps,
                  internal_analyticTag: analyticTag,
                }
              : nativeProps;
          return (
            <TextAncestor.Consumer>
              {hasTextAncestor => {
                if (hasTextAncestor) {
                  return (
                    <TextInlineImageNativeComponent
                      // $FlowFixMe[incompatible-type]
                      style={style}
                      resizeMode={resizeMode}
                      headers={nativeProps.headers}
                      src={sources}
                      ref={actualRef}
                    />
                  );
                }

                return (
                  <ImageViewNativeComponent
                    {...nativePropsWithAnalytics}
                    resizeMode={resizeMode}
                    ref={actualRef}
                  />
                );
              }}
            </TextAncestor.Consumer>
          );
        }}
      </ImageAnalyticsTagContext.Consumer>
    );
  },
);

const imageComponentDecorator = unstable_getImageComponentDecorator();
if (imageComponentDecorator != null) {
  BaseImage = imageComponentDecorator(BaseImage);
}

// QRN ADD 废弃类型补全
export type ImageComponentStatics = $ReadOnly<{|
    getSize: typeof getSize,
    getSizeWithHeaders: typeof getSizeWithHeaders,
    prefetch: typeof prefetch,
    prefetchWithMetadata: typeof prefetchWithMetadata,
    abortPrefetch: typeof abortPrefetch,
    queryCache: typeof queryCache,
    resolveAssetSource: typeof resolveAssetSource,
    // QRN ADD
    propTypes: typeof ImageProps,
    // QRN END
|}>;
// QRN END

// $FlowExpectedError[incompatible-type] Eventually we need to move these functions from statics of the component to exports in the module.
const Image: ImageAndroid = BaseImage;

Image.displayName = 'Image';

/**
 * Retrieve the width and height (in pixels) of an image prior to displaying it
 *
 * See https://reactnative.dev/docs/image#getsize
 */
// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.
Image.getSize = getSize;

/**
 * Retrieve the width and height (in pixels) of an image prior to displaying it
 * with the ability to provide the headers for the request
 *
 * See https://reactnative.dev/docs/image#getsizewithheaders
 */
// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.
Image.getSizeWithHeaders = getSizeWithHeaders;

/**
 * Prefetches a remote image for later use by downloading it to the disk
 * cache
 *
 * See https://reactnative.dev/docs/image#prefetch
 */
// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.
Image.prefetch = prefetch;

/**
 * Prefetches a remote image for later use by downloading it to the disk
 * cache, and adds metadata for queryRootName and rootTag.
 *
 * See https://reactnative.dev/docs/image#prefetch
 */
// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.
Image.prefetchWithMetadata = prefetchWithMetadata;

/**
 * Abort prefetch request.
 *
 * See https://reactnative.dev/docs/image#abortprefetch
 */
// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.
Image.abortPrefetch = abortPrefetch;

/**
 * Perform cache interrogation.
 *
 * See https://reactnative.dev/docs/image#querycache
 */
// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.
Image.queryCache = queryCache;

/**
 * Resolves an asset reference into an object.
 *
 * See https://reactnative.dev/docs/image#resolveassetsource
 */
// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.
Image.resolveAssetSource = resolveAssetSource;

// QRN ADD
Image.propTypes = ImageProps;
// QRN END

const styles = StyleSheet.create({
  base: {
    overflow: 'hidden',
  },
});

module.exports = Image;
