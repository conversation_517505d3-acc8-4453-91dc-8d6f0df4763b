/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 */

import type {ImageStyleProp} from '../StyleSheet/StyleSheet';
import type {RootTag} from '../Types/RootTagTypes';
import type {AbstractImageIOS, ImageIOS} from './ImageTypes.flow';
import type {ImageSize} from './NativeImageLoaderAndroid';

import {createRootTag} from '../ReactNative/RootTag';
import flattenStyle from '../StyleSheet/flattenStyle';
import StyleSheet from '../StyleSheet/StyleSheet';
import ImageAnalyticsTagContext from './ImageAnalyticsTagContext';
import {
  unstable_getImageComponentDecorator,
  useWrapRefWithImageAttachedCallbacks,
} from './ImageInjection';
import {getImageSourcesFromImageProps} from './ImageSourceUtils';
import {convertObjectFitToResizeMode} from './ImageUtils';
import ImageViewNativeComponent from './ImageViewNativeComponent';
import NativeImageLoaderIOS from './NativeImageLoaderIOS';
import resolveAssetSource from './resolveAssetSource';
import * as React from 'react';

// QRN ADD
let NativeImage, ImageView, isNewImage;
const PropTypes = require('prop-types');
const DeprecatedImagePropType = require('deprecated-react-native-prop-types/DeprecatedImagePropType');
try {
  NativeImage = require('./NativeImageLoaderIOS').default;

  if (!NativeImage) {
    throw new Error('请使用旧版 image');
  }

  ImageView = require('./ImageViewNativeComponent').default;
  isNewImage = true;
} catch (error) {
  NativeImage = NativeModules.ImageViewManager;
  ImageView = requireNativeComponent('RCTImageView');
  isNewImage = false;
}

const ImageProps = {
  ...DeprecatedImagePropType,
  /**
   * 图片的像素比，约高在高分辨屏幕上约清晰，设置为0时会根据屏幕默认设置
   * 这个参数并不会影响图片显示的位置和内容范围，只会影响用户看到的清晰度
   * 主要用于解决在 iPhone 4s、iPhone6 plus上内存占用过大的问题
   * @type PropTypes.number
   */
  imageScale: PropTypes.number,
  /**
   * Animated webp play or display first frame.
   * @type PropTypes.bool
   */
  animated: PropTypes.bool,
};
// QRN END

function getSize(
  uri: string,
  success?: (width: number, height: number) => void,
  failure?: (error: mixed) => void,
): void | Promise<ImageSize> {
	// QRN ADD 由于新旧传参不一样, 所以需要判断区分
	const safeSuccess = success || (() => {});
  if (isNewImage) {
    NativeImage.getSize(uri)
      .then(([width, height]) => safeSuccess(width, height))
      .catch(
        failure ||
        function () {
          console.warn('Failed to get size for image ' + uri);
        }
      );
  } else {
    NativeImage.getSize(
      uri,
      safeSuccess,
      failure ||
      function () {
        console.warn('Failed to get size for image: ' + uri);
      }
    );
  }
  // QRN END
}

function getSizeWithHeaders(
  uri: string,
  headers: {[string]: string, ...},
  success?: (width: number, height: number) => void,
  failure?: (error: mixed) => void,
): void | Promise<ImageSize> {
  // QRN ADD
  const safeSuccess = success || (() => {});
  if (isNewImage) {
    return NativeImage.getSizeWithHeaders(uri, headers)
      .then(function (sizes) {
        safeSuccess(sizes.width, sizes.height);
      })
      .catch(
        failure ||
        function () {
          console.warn('Failed to get size for image: ' + uri);
        }
      );
  } else {
    return NativeImage.getSizeWithHeaders({ uri, headers })
      .then(function (sizes) {
        safeSuccess(sizes.width, sizes.height);
      })
      .catch(
        failure ||
        function () {
          console.warn('Failed to get size for image: ' + uri);
        }
      );
  }
  // QRN End
}

function prefetchWithMetadata(
  url: string,
  queryRootName: string,
  rootTag?: ?RootTag,
): Promise<boolean> {
  // QRN ADD
  // 替换原生模块统一为兼容的 NativeImage
  if (NativeImage.prefetchImageWithMetadata) {
    // number params like rootTag cannot be nullable before TurboModules is available
    return NativeImage.prefetchImageWithMetadata(
      url,
      queryRootName,
      // NOTE: RootTag type
      rootTag != null ? rootTag : createRootTag(0),
    );
  } else {
    return NativeImage.prefetchImage(url);
  }
  // QRN END
}

function prefetch(url: string): Promise<boolean> {
  // QRN ADD
  // 替换原生模块统一为兼容的 NativeImage
  return NativeImage.prefetchImage(url);
  // QRN END
}

async function queryCache(
  urls: Array<string>,
): Promise<{[string]: 'memory' | 'disk' | 'disk/memory', ...}> {
  // QRN ADD
  // 替换原生模块统一为兼容的 NativeImage
  return NativeImage.queryCache(urls);
  // QRN END
}

/**
 * A React component for displaying different types of images,
 * including network images, static resources, temporary local images, and
 * images from local disk, such as the camera roll.
 *
 * See https://reactnative.dev/docs/image
 */
let BaseImage: AbstractImageIOS = React.forwardRef((props, forwardedRef) => {
  const source = getImageSourcesFromImageProps(props) || {
    uri: undefined,
    width: undefined,
    height: undefined,
  };

  let style: ImageStyleProp;
  let sources;
  if (Array.isArray(source)) {
    style = [styles.base, props.style];
    sources = source;
  } else {
    const {uri} = source;
    if (uri === '') {
      console.warn('source.uri should not be an empty string');
    }
    const width = source.width ?? props.width;
    const height = source.height ?? props.height;
    style = [{width, height}, styles.base, props.style];
    sources = [source];
  }

  const flattenedStyle = flattenStyle<ImageStyleProp>(style);
  const objectFit = convertObjectFitToResizeMode(flattenedStyle?.objectFit);
  const resizeMode =
    objectFit || props.resizeMode || flattenedStyle?.resizeMode || 'cover';
  const tintColor = props.tintColor ?? flattenedStyle?.tintColor;

  if (props.children != null) {
    throw new Error(
      'The <Image> component cannot contain children. If you want to render content on top of the image, consider using the <ImageBackground> component or absolute positioning.',
    );
  }
  const {
    'aria-busy': ariaBusy,
    'aria-checked': ariaChecked,
    'aria-disabled': ariaDisabled,
    'aria-expanded': ariaExpanded,
    'aria-selected': ariaSelected,
    src,
    ...restProps
  } = props;

  const _accessibilityState = {
    busy: ariaBusy ?? props.accessibilityState?.busy,
    checked: ariaChecked ?? props.accessibilityState?.checked,
    disabled: ariaDisabled ?? props.accessibilityState?.disabled,
    expanded: ariaExpanded ?? props.accessibilityState?.expanded,
    selected: ariaSelected ?? props.accessibilityState?.selected,
  };
  const accessibilityLabel = props['aria-label'] ?? props.accessibilityLabel;

  const actualRef = useWrapRefWithImageAttachedCallbacks(forwardedRef);

  return (
    <ImageAnalyticsTagContext.Consumer>
      {analyticTag => {
        return (
          // QRN ADD
          <ImageView
          // QRN END
            accessibilityState={_accessibilityState}
            {...restProps}
            accessible={props.alt !== undefined ? true : props.accessible}
            accessibilityLabel={accessibilityLabel ?? props.alt}
            ref={actualRef}
            style={style}
            resizeMode={resizeMode}
            tintColor={tintColor}
            source={sources}
            internal_analyticTag={analyticTag}
          />
        );
      }}
    </ImageAnalyticsTagContext.Consumer>
  );
});

const imageComponentDecorator = unstable_getImageComponentDecorator();
if (imageComponentDecorator != null) {
  BaseImage = imageComponentDecorator(BaseImage);
}

// QRN ADD 废弃类型补全
export type ImageComponentStatics = $ReadOnly<{|
    getSize: typeof getSize,
    getSizeWithHeaders: typeof getSizeWithHeaders,
    prefetch: typeof prefetch,
    prefetchWithMetadata: typeof prefetchWithMetadata,
    abortPrefetch: typeof abortPrefetch,
    queryCache: typeof queryCache,
    resolveAssetSource: typeof resolveAssetSource,
    // QRN ADD
    propTypes: typeof ImageProps,
    // QRN END
|}>;
// QRN END

// $FlowExpectedError[incompatible-type] Eventually we need to move these functions from statics of the component to exports in the module.
const Image: ImageIOS = BaseImage;

Image.displayName = 'Image';

// QRN ADD
Image.propTypes = ImageProps;
// QRN END

/**
 * Retrieve the width and height (in pixels) of an image prior to displaying it.
 *
 * See https://reactnative.dev/docs/image#getsize
 */
// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.
Image.getSize = getSize;

/**
 * Retrieve the width and height (in pixels) of an image prior to displaying it
 * with the ability to provide the headers for the request.
 *
 * See https://reactnative.dev/docs/image#getsizewithheaders
 */
// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.
Image.getSizeWithHeaders = getSizeWithHeaders;

/**
 * Prefetches a remote image for later use by downloading it to the disk
 * cache.
 *
 * See https://reactnative.dev/docs/image#prefetch
 */
// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.
Image.prefetch = prefetch;

/**
 * Prefetches a remote image for later use by downloading it to the disk
 * cache, and adds metadata for queryRootName and rootTag.
 *
 * See https://reactnative.dev/docs/image#prefetch
 */
// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.
Image.prefetchWithMetadata = prefetchWithMetadata;

/**
 * Performs cache interrogation.
 *
 *  See https://reactnative.dev/docs/image#querycache
 */
// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.
Image.queryCache = queryCache;

/**
 * Resolves an asset reference into an object.
 *
 * See https://reactnative.dev/docs/image#resolveassetsource
 */
// $FlowFixMe[incompatible-use] This property isn't writable but we're actually defining it here for the first time.
Image.resolveAssetSource = resolveAssetSource;

const styles = StyleSheet.create({
  base: {
    overflow: 'hidden',
  },
});

module.exports = Image;
