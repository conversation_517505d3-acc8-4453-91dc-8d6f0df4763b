/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow
 * @format
 */

import type {ViewStyleProp} from '../StyleSheet/StyleSheet';
import type {RootTag} from '../Types/RootTagTypes';
import type {IPerformanceLogger} from '../Utilities/createPerformanceLogger';
import type {DisplayModeType} from './DisplayMode';

import BugReporting from '../BugReporting/BugReporting';
import registerCallableModule from '../Core/registerCallableModule';
import createPerformanceLogger from '../Utilities/createPerformanceLogger';
import infoLog from '../Utilities/infoLog';
import SceneTracker from '../Utilities/SceneTracker';
import {coerceDisplayMode} from './DisplayMode';
import HeadlessJsTaskError from './HeadlessJsTaskError';
import NativeHeadlessJsTaskSupport from './NativeHeadlessJsTaskSupport';
import renderApplication from './renderApplication';
import {unmountComponentAtNodeAndRemoveContainer} from './RendererProxy';
import invariant from 'invariant';

// QRN ADD
import RCTDeviceEventEmitter from '../EventEmitter/RCTDeviceEventEmitter';
const BackHandler = require('../Utilities/BackHandler');
const ToastAndroid = require('../Components/ToastAndroid/ToastAndroid');
const NativeModules = require('../BatchedBridge/NativeModules');
let qrnFirstRootTag = 0;
// QRN END

type Task = (taskData: any) => Promise<void>;
export type TaskProvider = () => Task;
type TaskCanceller = () => void;
type TaskCancelProvider = () => TaskCanceller;

export type ComponentProvider = () => React$ComponentType<any>;
export type ComponentProviderInstrumentationHook = (
  component_: ComponentProvider,
  scopedPerformanceLogger: IPerformanceLogger,
) => React$ComponentType<any>;
export type AppConfig = {
  appKey: string,
  component?: ComponentProvider,
  run?: Runnable,
  section?: boolean,
  ...
};
type AppParameters = {
  initialProps: $ReadOnly<{[string]: mixed, ...}>,
  rootTag: RootTag,
  fabric?: boolean,
};
export type Runnable = (
  appParameters: AppParameters,
  displayMode: DisplayModeType,
) => void;
export type Runnables = {[appKey: string]: Runnable};
export type Registry = {
  sections: $ReadOnlyArray<string>,
  runnables: Runnables,
  ...
};
export type WrapperComponentProvider = (
  appParameters: Object,
) => React$ComponentType<any>;
export type RootViewStyleProvider = (appParameters: Object) => ViewStyleProp;

const runnables: Runnables = {};
let runCount = 1;
const sections: Runnables = {};
const taskProviders: Map<string, TaskProvider> = new Map();
const taskCancelProviders: Map<string, TaskCancelProvider> = new Map();
let componentProviderInstrumentationHook: ComponentProviderInstrumentationHook =
  (component: ComponentProvider) => component();

let wrapperComponentProvider: ?WrapperComponentProvider;
let rootViewStyleProvider: ?RootViewStyleProvider;

// QRN ADD
const logBaseData = {
  bizTag: 'APP',
  operType: 'monitor',
  bizType: 'app',
  appcode: 'pageload_monitor',
  module: 'default',
  page: 'pageload',
  operTime: '*',
  id: 'ramBundleLoadTime',
  ext: {}
};
// QRN END

/**
 * `AppRegistry` is the JavaScript entry point to running all React Native apps.
 *
 * See https://reactnative.dev/docs/appregistry
 */
const AppRegistry = {
  setWrapperComponentProvider(provider: WrapperComponentProvider) {
    wrapperComponentProvider = provider;
  },

  setRootViewStyleProvider(provider: RootViewStyleProvider) {
    rootViewStyleProvider = provider;
  },

  registerConfig(config: Array<AppConfig>): void {
    config.forEach(appConfig => {
      if (appConfig.run) {
        AppRegistry.registerRunnable(appConfig.appKey, appConfig.run);
      } else {
        invariant(
          appConfig.component != null,
          'AppRegistry.registerConfig(...): Every config is expected to set ' +
            'either `run` or `component`, but `%s` has neither.',
          appConfig.appKey,
        );
        AppRegistry.registerComponent(
          appConfig.appKey,
          appConfig.component,
          appConfig.section,
        );
      }
    });
  },

  /**
   * Registers an app's root component.
   *
   * See https://reactnative.dev/docs/appregistry#registercomponent
   */
  registerComponent(
    appKey: string,
    componentProvider: ComponentProvider,
    section?: boolean,
  ): string {
    const scopedPerformanceLogger = createPerformanceLogger();
    runnables[appKey] = (appParameters, displayMode) => {
      // QRN ADD 缓存首页的 tag
      if (!qrnFirstRootTag) {
        qrnFirstRootTag = appParameters.rootTag;
      }
      // QRN END

      renderApplication(
        componentProviderInstrumentationHook(
          componentProvider,
          scopedPerformanceLogger,
        ),
        appParameters.initialProps,
        appParameters.rootTag,
        wrapperComponentProvider && wrapperComponentProvider(appParameters),
        rootViewStyleProvider && rootViewStyleProvider(appParameters),
        appParameters.fabric,
        scopedPerformanceLogger,
        appKey === 'LogBox', // is logbox
        appKey,
        displayMode,
      );
    };
    if (section) {
      sections[appKey] = runnables[appKey];
    }
    // QRN ADD 上报页面注册
    if (appKey) {
      reportQrnPages({ id: 'qrn_pages_registered', pageName: appKey });
    }
    // QRN END
    return appKey;
  },

  registerRunnable(appKey: string, run: Runnable): string {
    runnables[appKey] = run;
    return appKey;
  },

  registerSection(appKey: string, component: ComponentProvider): void {
    AppRegistry.registerComponent(appKey, component, true);
  },

  getAppKeys(): $ReadOnlyArray<string> {
    return Object.keys(runnables);
  },

  getSectionKeys(): $ReadOnlyArray<string> {
    return Object.keys(sections);
  },

  getSections(): Runnables {
    return {
      ...sections,
    };
  },

  getRunnable(appKey: string): ?Runnable {
    return runnables[appKey];
  },

  getRegistry(): Registry {
    return {
      sections: AppRegistry.getSectionKeys(),
      runnables: {...runnables},
    };
  },

  setComponentProviderInstrumentationHook(
    hook: ComponentProviderInstrumentationHook,
  ) {
    componentProviderInstrumentationHook = hook;
  },

  /**
   * Loads the JavaScript bundle and runs the app.
   *
   * See https://reactnative.dev/docs/appregistry#runapplication
   */
  runApplication(
    appKey: string,
    appParameters: AppParameters,
    displayMode?: number,
  ): void {
    if (appKey !== 'LogBox') {
      const logParams = __DEV__ ? ` with ${JSON.stringify(appParameters)}` : '';
      const msg = `Running "${appKey}"${logParams}`;
      infoLog(msg);
      BugReporting.addSource(
        'AppRegistry.runApplication' + runCount++,
        () => msg,
      );
    }

    // QRN ADD 进入页面时上报 QP 信息
    const param = {
      'hybridId': global.__QP_INFO ? global.__QP_INFO.origin_hybridid : '',
      'qpVersion': global.__QP_INFO ? global.__QP_INFO.version : '0',
    }

    NativeModules.QRNUrs &&
    NativeModules.QRNUrs.publishAction &&
    NativeModules.QRNUrs.publishAction(appParameters?.rootTag, 'C_UPLOAD_CQP', param, () => { })
    // QRN END

    // QRN ADD
    global.__isFabric = appParameters.fabric ?? false;
    // QRN END

    // QRN ADD 传入__lifecycleListenerName, 为函数式组件提供生命周期支持
    const __lifecycleListenerName = `${appKey}_${appParameters.rootTag}`;
    if (!appParameters.hasOwnProperty('initialProps')) {
      appParameters['initialProps'] = {};
    }
    appParameters.initialProps['__qrnLifecycleListenerName'] = __lifecycleListenerName;
    appParameters.initialProps['rootTag'] = appParameters.rootTag;
    // QRN END

    // QRN ADD 页面数据采集开关
    if (appParameters && appParameters.hasOwnProperty('__qrnViewMonitorEnable')) {
      global.__qrnViewMonitorEnable = appParameters['__qrnViewMonitorEnable'];
    }
    // QRN END

    // QRN ADD 上报页面未注册信息
    const runnablesKeys = Object.keys(runnables).join(',');
    if (!runnables[appKey] || !runnables[appKey].run) {
      const requireModulesLogData = {
        bizTag: 'APP',
        operType: 'monitor',
        bizType: 'app',
        appcode: 'runApplication_monitor',
        module: 'default',
        page: 'AppRegistry',
        operTime: '*',
        id: 'AppRunApplication',
        ext: {
          appKey: appKey,
          runnables: runnablesKeys
        }
      };
      NativeModules.QAV && NativeModules.QAV.componentLog(requireModulesLogData);
    }
    // QRN END

    invariant(
      runnables[appKey],
      `"${appKey}" has not been registered. This can happen if:\n` +
        '* Metro (the local dev server) is run from the wrong folder. ' +
        'Check if Metro is running, stop it and restart it in the current project.\n' +
        "* A module failed to load due to an error and `AppRegistry.registerComponent` wasn't called.",
    );

    SceneTracker.setActiveScene({name: appKey});
    runnables[appKey](appParameters, coerceDisplayMode(displayMode));

    // QRN ADD 上报页面打开
    if (runnables && runnables[appKey]) {
      reportQrnPages({ id: 'qrn_pages_visited', pageName: appKey });
    }
    // QRN END
  },

  /**
   * Update initial props for a surface that's already rendered
   */
  setSurfaceProps(
    appKey: string,
    appParameters: Object,
    displayMode?: number,
  ): void {
    if (appKey !== 'LogBox') {
      const msg =
        'Updating props for Surface "' +
        appKey +
        '" with ' +
        JSON.stringify(appParameters);
      infoLog(msg);
      BugReporting.addSource(
        'AppRegistry.setSurfaceProps' + runCount++,
        () => msg,
      );
    }
    invariant(
      runnables[appKey],
      `"${appKey}" has not been registered. This can happen if:\n` +
        '* Metro (the local dev server) is run from the wrong folder. ' +
        'Check if Metro is running, stop it and restart it in the current project.\n' +
        "* A module failed to load due to an error and `AppRegistry.registerComponent` wasn't called.",
    );

    runnables[appKey](appParameters, coerceDisplayMode(displayMode));
  },

  /**
   * Stops an application when a view should be destroyed.
   *
   * See https://reactnative.dev/docs/appregistry#unmountapplicationcomponentatroottag
   */
  unmountApplicationComponentAtRootTag(rootTag: RootTag): void {
    unmountComponentAtNodeAndRemoveContainer(rootTag);

    // QRN ADD 上报时间点
    if (global._q_ram_bundle_first_load_script_end) {
	    const loadScriptLogData = Object.assign({}, logBaseData, {
	      ext: {
	        loadEnd: global._q_ram_bundle_first_load_script_end || 0,
	        loadStart: global._q_ram_bundle_first_load_script_start || 0,
	        loadFrom: 'js',
	        loadType: 'ram_bundle_first_load_script'
	      }
	    });
	    NativeModules.QAV.componentLog(loadScriptLogData);
	  }

    if (
      global._q_ram_bundle_pre_load_time_array &&
      global._q_ram_bundle_pre_load_time_array.length
    ) {
      for (let i = 0; i < global._q_ram_bundle_pre_load_time_array.length; i++) {
        const element = global._q_ram_bundle_pre_load_time_array[i];

        const preLoadLogData = Object.assign({}, logBaseData, {
          ext: {
            loadEnd: element[1] || 0,
            loadStart: element[0] || 0,
            loadFrom: 'js',
            loadType: 'ram_bundle_pre_load'
          }
        });
        NativeModules.QAV.componentLog(preLoadLogData);
      }
    }
    // QRN END

    const OriginDeviceInfo = NativeModules.QRCTDeviceInfo;
    const isBetaOrDev =
      OriginDeviceInfo.releaseType === 'dev' || OriginDeviceInfo.releaseType === 'beta';

    // QRN ADD 首页退出时 1.弹出当前所以正在监听的事件 2.检测全局对象是否超出自身50% 仅 beta&dev 有效
    if (isBetaOrDev) {
      if (qrnFirstRootTag === rootTag) {
        try {
          // 展示当前未移除的事件监听
          // RN68 之后的版本，会在 unmountComponentAtNodeAndRemoveContainer 时，自动移除所有的事件监听
          // _showCurrentEventListener();

          // 检测全局对象是否超出自身50%
          _checkSizeOfGlobal();

          // 上报 require 模块
          _reportRequireModules();
        } catch (error) {
          throw new Error(error);
        }
        qrnFirstRootTag = 0;
      }
    }
    // QRN END
  },

  /**
   * Register a headless task. A headless task is a bit of code that runs without a UI.
   *
   * See https://reactnative.dev/docs/appregistry#registerheadlesstask
   */
  registerHeadlessTask(taskKey: string, taskProvider: TaskProvider): void {
    // $FlowFixMe[object-this-reference]
    this.registerCancellableHeadlessTask(taskKey, taskProvider, () => () => {
      /* Cancel is no-op */
    });
  },

  /**
   * Register a cancellable headless task. A headless task is a bit of code that runs without a UI.
   *
   * See https://reactnative.dev/docs/appregistry#registercancellableheadlesstask
   */
  registerCancellableHeadlessTask(
    taskKey: string,
    taskProvider: TaskProvider,
    taskCancelProvider: TaskCancelProvider,
  ): void {
    if (taskProviders.has(taskKey)) {
      console.warn(
        `registerHeadlessTask or registerCancellableHeadlessTask called multiple times for same key '${taskKey}'`,
      );
    }
    taskProviders.set(taskKey, taskProvider);
    taskCancelProviders.set(taskKey, taskCancelProvider);
  },

  /**
   * Only called from native code. Starts a headless task.
   *
   * See https://reactnative.dev/docs/appregistry#startheadlesstask
   */
  startHeadlessTask(taskId: number, taskKey: string, data: any): void {
    const taskProvider = taskProviders.get(taskKey);
    if (!taskProvider) {
      console.warn(`No task registered for key ${taskKey}`);
      if (NativeHeadlessJsTaskSupport) {
        NativeHeadlessJsTaskSupport.notifyTaskFinished(taskId);
      }
      return;
    }
    taskProvider()(data)
      .then(() => {
        if (NativeHeadlessJsTaskSupport) {
          NativeHeadlessJsTaskSupport.notifyTaskFinished(taskId);
        }
      })
      .catch(reason => {
        console.error(reason);

        if (
          NativeHeadlessJsTaskSupport &&
          reason instanceof HeadlessJsTaskError
        ) {
          // $FlowFixMe[unused-promise]
          NativeHeadlessJsTaskSupport.notifyTaskRetry(taskId).then(
            retryPosted => {
              if (!retryPosted) {
                NativeHeadlessJsTaskSupport.notifyTaskFinished(taskId);
              }
            },
          );
        }
      });
  },

  /**
   * Only called from native code. Cancels a headless task.
   *
   * See https://reactnative.dev/docs/appregistry#cancelheadlesstask
   */
  cancelHeadlessTask(taskId: number, taskKey: string): void {
    const taskCancelProvider = taskCancelProviders.get(taskKey);
    if (!taskCancelProvider) {
      throw new Error(`No task canceller registered for key '${taskKey}'`);
    }
    taskCancelProvider()();
  },
};

// Register LogBox as a default surface
AppRegistry.registerComponent('LogBox', () => {
  if (__DEV__ && typeof jest === 'undefined') {
    return require('../LogBox/LogBoxInspectorContainer').default;
  } else {
    return function NoOp() {
      return null;
    };
  }
});

// QRN ADD 首页退出时 检测全局对象是否超出自身50%
function _checkSizeOfGlobal() {
  const sizeOfGlobal = roughSizeOfObject(global);

  if (global.__q_firstSizeOfGlobal) {
    // 超出 50%, 弹窗警告
    if (sizeOfGlobal - global.__q_firstSizeOfGlobal > global.__q_firstSizeOfGlobal / 2) {
      showWarnning('QRN 提醒: 注意! Global对象存在内存泄漏!');
    }
  } else {
    global.__q_firstSizeOfGlobal = sizeOfGlobal;
  }
}
// QRN END

/**
 * QRN ADD
 * 计算 Object 大小
 */
function roughSizeOfObject(object) {
  var objectList = [];
  var stack = [object];
  var bytes = 0;

  while (stack.length) {
    var value = stack.pop();

    if (typeof value === 'boolean') {
      bytes += 4;
    } else if (typeof value === 'string') {
      bytes += value.length * 2;
    } else if (typeof value === 'number') {
      bytes += 8;
    } else if (typeof value === 'object' && objectList.indexOf(value) === -1) {
      objectList.push(value);

      for (var i in value) {
        stack.push(value[i]);
      }
    }
  }
  return bytes;
}
// QRN END

/**
 * QRN ADD
 * 展示当前正在进行的事件监听
 */
function _showCurrentEventListener() {
  const curListener = RCTDeviceEventEmitter.sharedSubscriber._subscriptionsForType || {};

  const alertContent = [],
    logContent = [];

  if (!global.__q_firstListenerCntCache) {
    global.__q_firstListenerCntCache = {};
  }

  const effList = [];

  Object.keys(curListener).forEach((key) => {
    const listernerList = curListener[key];
    const fullList = listernerList.filter((item) => !!item);

    if (!fullList.length) return;

    if (!global.__q_firstListenerCntCache[key]) {
      global.__q_firstListenerCntCache[key] = fullList.length;
    }

    effList.push({
      key,
      len: fullList.length
    });

    // 较最初比较, 超过 2 个之后 弹窗警告
    if (fullList.length > global.__q_firstListenerCntCache[key] + 2) {
      const d = {};
      d[key] = fullList;
      logContent.push(d);
      alertContent.push(`${key}: ${fullList.length}`);
    }
  });

  if (effList.length) {
    // 上报 QAV
    const hybridid = global.__QP_INFO && global.__QP_INFO.hybridid;
    const OriginDeviceInfo = NativeModules.QRCTDeviceInfo;

    const LogData = {
      bizTag: 'APP',
      operType: 'monitor',
      bizType: 'app',
      appcode: 'unmountApplication_unremoved_listerner_monitor',
      module: 'default',
      page: 'AppRegistry',
      operTime: '*',
      id: 'unmountApplication_unremoved_listerner',
      ext: {
        listener: effList,
        hybridid: hybridid || '',
        releaseType: OriginDeviceInfo.releaseType
      }
    };

    NativeModules.QAV && NativeModules.QAV.componentLog(LogData);
  }

  if (alertContent.length) {
    const warnText = 'QRN 提醒! 当前未移除的监听事件: ';
    console.log(warnText, logContent);
    alertContent.unshift(warnText);

    showWarnning(alertContent.join('\n'));
  }
}

function showWarnning(msg) {
  const isIOS = Platform.OS === 'ios';
  // 弹窗样式
  const options = {
    backgroundColor: '#000000',
    textFontSize: 16,
    textMaxLines: 100,
    textFontColor: '#FFFF00',
    position: 1
  };

  if (isIOS) {
    NativeModules.QDToastiOS.show(msg, '', 3500, options);
  } else {
    NativeModules.QDToastAndroid.showGlobalToast(msg, null, ToastAndroid.LONG, options);
  }
}

function reportQrnPages(option) {
  const { id, pageName } = option;
  // qrn页面访问埋点
  if (global.__QP_INFO) {
    const { hybridid, version } = global.__QP_INFO;

    const qrnPagesData = {
      bizType: 'app',
      bizTag: 'APP',
      module: 'default',
      appcode: 'qrn_js',
      page: 'qrn_pages',
      id,
      operType: 'monitor',
      operTime: '*',
      ext: {
        hybridId: hybridid,
        pageName,
        version,
        rtag: global.__BTAG_BIZ || ''
      }
    };

    NativeModules.QAV && NativeModules.QAV.componentLog(qrnPagesData);
  }
}

// QRN ADD 项目推出时 统计 require 模块
function _reportRequireModules() {
  if (!global.__q_requireModuleIds) {
    return;
  }

  const modules = Array.from(global.__q_requireModuleIds);

  const requireModulesLogData = {
    bizTag: 'APP',
    operType: 'show,monitor',
    bizType: 'app',
    appcode: 'qrn_js',
    module: 'default',
    page: 'qrn_pages',
    operTime: '*',
    id: 'unmountApplication_require_modules',
    ext: {
      requireModules: modules
    }
  };

  NativeModules.QAV && NativeModules.QAV.componentLog(requireModulesLogData);
}
// QRN END

global.RN$AppRegistry = AppRegistry;

// Backwards compat with SurfaceRegistry, remove me later
global.RN$SurfaceRegistry = {
  renderSurface: AppRegistry.runApplication,
  setSurfaceProps: AppRegistry.setSurfaceProps,
};

registerCallableModule('AppRegistry', AppRegistry);

module.exports = AppRegistry;
