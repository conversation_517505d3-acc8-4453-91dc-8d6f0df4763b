// QRN ADD 文件移除补充
/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 * @flow strict-local
 */

import UIManager from '../ReactNative/UIManager';

/**
 * Adds a deprecation warning when the prop is used.
 */
function deprecatedPropType(
  propType: ReactPropsCheckType,
  explanation: string,
): ReactPropsCheckType {
  return function validate(props, propName, componentName, ...rest) {
    // Don't warn for native components.
    if (
      global.RN$Bridgeless !== true &&
      !UIManager.hasViewManagerConfig(componentName) &&
      props[propName] !== undefined
    ) {
      console.warn(
        `\`${propName}\` supplied to \`${componentName}\` has been deprecated. ${explanation}`,
      );
    }

    return propType(props, propName, componentName, ...rest);
  };
}

module.exports = deprecatedPropType;
// QRN END