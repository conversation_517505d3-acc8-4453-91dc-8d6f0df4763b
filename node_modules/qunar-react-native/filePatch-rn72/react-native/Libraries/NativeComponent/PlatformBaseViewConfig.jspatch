/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 * @flow strict-local
 */

import type { PartialViewConfig } from '../Renderer/shims/ReactNativeTypes';

import BaseViewConfig from './BaseViewConfig';

export type PartialViewConfigWithoutName = $Rest<
    PartialViewConfig,
    { uiViewClassName: string },
>;

const PlatformBaseViewConfig: PartialViewConfigWithoutName = BaseViewConfig;

// QRN ADD 追加自定义属性
BaseViewConfig.validAttributes = {
    ...BaseViewConfig.validAttributes,
    // QRN ADD 自定义属性
    qExposeId: true,
    qExposeData: true,
    qAutoZoom: true,
    _ID_: true,
    qRenderMarker: true,
    qDisableFlatten: true,
    useQrnMeasure: true,
    qDisplayNode: true,
    qShouldStartLoadWithRequest: true,
    qIconFontAlternateCode: true,
    // QRN END
}
// QRN END

// In Wilde/FB4A, use RNHostComponentListRoute in Bridge mode to verify
// whether the JS props defined here match the native props defined
// in RCTViewManagers in iOS, and ViewManagers in Android.
export default PlatformBaseViewConfig;
