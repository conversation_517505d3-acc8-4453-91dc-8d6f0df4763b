import React, { Component } from 'react';
import { PropTypes } from 'prop-types';
// import ReactNative, { PanResponder, View, StyleSheet, Dimensions } from 'react-native';  //适配鸿蒙
import { PanResponder, View, StyleSheet, Dimensions } from 'react-native';
import rebound from 'rebound';
import extend from 'extend';

var released = true;
var distinct = 0;
var previousPage = 0;
var timer = null;

const TERMINATION_REQUEST_OFFSET_X = 20;
const TERMINATION_REQUEST_OFFSET_Y = 10;
const SPRING_TENSION = 600;
const SPRING_FRICTION = 50;
const PAGE_CHANGE_THRESHOLD_MAX = 50;
const PAGE_CHANGE_THRESHOLD_MIN = 10;

class CarouselPager extends Component {
    constructor(props) {
        super(props);

        this._panResponder = {};
        this._scrollSpring = null;
        this.springSystem = null;
        this.autoplayInterval = null;

        this.width = Dimensions.get('window');
        this.state = {
            currentPage: 0,
            childrenLength: 0
        };
        this.scrolling = false;

        this._checkCanMove = this._checkCanMove.bind(this);
    }

    componentWillUnmount() {
        this._scrollSpring.removeAllListeners();
        this._autoPlayStop.bind(this)();
        if (timer) {
            clearTimeout(timer);
        }
    }

    // 之前的 componentWillMount 方法
    oldComponentWillMount() {
        this.width = this.props.width;
        this._panResponder = PanResponder.create(
            extend(
                {
                    onStartShouldSetPanResponder: this._handleStartShouldSetPanResponder.bind(this),
                    onPanResponderMove: this._handlePanResponderMove.bind(this),
                    onPanResponderRelease: this._handlePanResponderEnd.bind(this),
                    onPanResponderTerminate: this._handlePanResponderEnd.bind(this),
                    onStartShouldSetPanResponderCapture: this._hendleResponderCapture.bind(this),
                    onMoveShouldSetPanResponderCapture: this._hendleResponderCapture.bind(this),
                    onPanResponderTerminationRequest: this._handleTerminationRequest.bind(this)
                },
                this.props.gestureResponder || {}
            )
        );

        this.springSystem = new rebound.SpringSystem();
        this._scrollSpring = this.springSystem.createSpring(SPRING_TENSION, SPRING_FRICTION);
        var that = this;
        this._scrollSpring.addListener({
            onSpringUpdate: () => {
                if (this.released) {
                    this._previousLeft = this._scrollSpring.getCurrentValue();
                    this.refs.scrollPanel &&
                        this.refs.scrollPanel.setNativeProps({
                            style: {
                                left: this._scrollSpring.getCurrentValue()
                            }
                        });
                }
            },
            onSpringEndStateChange: () => {
                var that = this;
                if (that.props.speed) {
                    timer = setTimeout(function () {
                        var currentPage = Math.floor(
                            (that._previousLeft + this.width / 2) / this.width
                        );

                        currentPage--;
                        if (currentPage < this.state.childrenLength * -1) {
                            currentPage = -1;
                            that._scrollSpring.setCurrentValue((currentPage + 1) * this.width);
                        }
                        that.movePage(currentPage);
                    }, that.props.speed);
                }
            }
        });

        this._init.bind(this)();
    }

    componentDidUpdate(prevProps, prevState) {
        const { reset, autoplay } = this.props;
        if (reset) {
            // 异步执行，来强制在更新数据之后 reset
            setTimeout(() => {
                this._init.bind(this)();

                this._autoPlayStop.bind(this)();
                this._autoPlayStart.bind(this)();
            }, 0);
        }
        if (prevProps.autoplay && !autoplay) {
            this._autoPlayStart.bind(this)();
        } else if (!prevProps.autoplay && autoplay) {
            this._autoPlayStart.bind(this)(true);
        }
    }

    _init() {
        const { initialIndex } = this.props;

        this.setState(
            {
                currentPage: this.props.initialIndex + 1,
                childrenLength: this.props.children.length
            },
            () => {
                this.refs.scrollPanel &&
                    this.refs.scrollPanel.setNativeProps({
                        style: {
                            left: -this.props.initialIndex * this.width
                        }
                    });
                this._previousLeft = -this.props.initialIndex * this.width;
                this._scrollSpring.setCurrentValue(this._previousLeft);

                if (this.props.onChange) {
                    this.props.onChange(this.state.currentPage - 1);
                }
            }
        );
    }

    onPressSlide(index) {
        if (this.props.onPress) {
            this.props.onPress(index - 1);
        }
    }

    _handleTerminationRequest(e: Object, gestureState: Object): boolean {
        return (
            Math.abs(gestureState.dx) < TERMINATION_REQUEST_OFFSET_X &&
            Math.abs(gestureState.dy) > TERMINATION_REQUEST_OFFSET_Y
        );
    }

    _hendleResponderCapture(e: Object, gestureState: Object): boolean {
        return Math.abs(gestureState.dx) > this.props.moveOffsetTreshold;
    }

    _handleStartShouldSetPanResponder(e: Object, gestureState: Object): boolean {
        // Should we become active when the user presses down on the circle?
        distinct = 0;
        if (timer) {
            clearTimeout(timer);
        }
        return true;
    }

    _handlePanResponderMove(e: Object, gestureState: Object) {
        if (timer) {
            clearTimeout(timer);
        }

        this.released = false;
        if (!this._checkCanMove(gestureState.dx)) {
            return;
        } else {
            this.scrolling = true;
        }

        this._autoPlayStop.bind(this)();

        distinct += gestureState.dx;
        if (gestureState.dx + this._previousLeft < -1 * (this.width * this.state.childrenLength)) {
            this._previousLeft = gestureState.dx * -1;
        }

        if (gestureState.dx + this._previousLeft > 0) {
            this._previousLeft = this.width * -this.state.childrenLength - gestureState.dx;
        }

        this.refs.scrollPanel &&
            this.refs.scrollPanel.setNativeProps({
                style: {
                    left: gestureState.dx + this._previousLeft
                }
            });
        this._scrollSpring.setCurrentValue(gestureState.dx + this._previousLeft);
        //    this._updatePosition();
    }

    componentDidMount() {
        this.oldComponentWillMount();
        this._autoPlayStart.bind(this)();
        Dimensions.addEventListener('change', () => {
            setTimeout(() => {
                this.movePage(-this.state.currentPage + 1, true);
            }, 300);
        });
    }

    _autoPlayStart(forceStart) {
        if (this.props.autoplay || forceStart) {
            clearInterval(this.autoPlayInteval);
            this.autoPlayInteval = setInterval(() => {
                if (this.state.currentPage === 1) {
                    this.refs.scrollPanel &&
                        this.refs.scrollPanel.setNativeProps({
                            style: { left: 0 }
                        });
                    this._scrollSpring.setCurrentValue(0);
                }

                this.released = true;
                this.movePage(-this.state.currentPage);
            }, this.props.autoplayInterval);
        }
    }

    _autoPlayStop() {
        clearInterval(this.autoPlayInteval);
        this.autoPlayInteval = null;
    }

    _handlePanResponderEnd(e: Object, gestureState: Object) {
        this.released = true;
        this.scrolling = false;

        if (!this._checkCanMove(gestureState.dx)) {
            this.refs.scrollPanel &&
                this.refs.scrollPanel.setNativeProps({
                    style: {
                        left: -(this.state.currentPage - 1) * this.width
                    }
                });

            if (
                Math.abs(gestureState.dx) < PAGE_CHANGE_THRESHOLD_MIN &&
                Math.abs(gestureState.dy) < PAGE_CHANGE_THRESHOLD_MIN
            ) {
                this.onPressSlide(this.state.currentPage);
            }

            return;
        }
        this._previousLeft += gestureState.dx;

        var currentPage = Math.floor((this._previousLeft + this.width / 2) / this.width);
        if (
            currentPage == previousPage ||
            Math.abs(currentPage - previousPage) === this.props.children.length
        ) {
            if (gestureState.dx > PAGE_CHANGE_THRESHOLD_MAX) {
                currentPage++;
            } else if (gestureState.dx < -PAGE_CHANGE_THRESHOLD_MAX) {
                currentPage--;
            } else {
                var realCurrentPage = (currentPage * -1 + 1) % (this.state.childrenLength + 1);
                if (realCurrentPage == 0) {
                    realCurrentPage = 1;
                }
                if (
                    Math.abs(gestureState.dx) < PAGE_CHANGE_THRESHOLD_MIN &&
                    Math.abs(gestureState.dy) < PAGE_CHANGE_THRESHOLD_MIN
                ) {
                    this.onPressSlide(realCurrentPage);
                }
            }
        }

        this.movePage(currentPage, true); // user swipe
        this._autoPlayStart.bind(this)();

        return false;
    }

    _checkCanMove(direction) {
        let loop = this.props.loop,
            currentpage = this.state.currentPage,
            childrenLength = this.props.children.length;

        if (Math.abs(direction) < this.props.moveOffsetTreshold && !this.scrolling) {
            return false;
        }
        if (childrenLength === 1) {
            return false;
        }
        if (loop) {
            return true;
        }
        if (
            (currentpage === 1 && direction > 0) ||
            (currentpage === childrenLength && direction < 0)
        ) {
            return false;
        }
        return true;
    }

    movePage(currentPage, isUserSwipe) {
        previousPage = currentPage;
        this._scrollSpring.setEndValue(currentPage * this.width);

        this._currentPage = currentPage * -1 + 1;
        if (this._currentPage > this.state.childrenLength) {
            this._currentPage = 1;
        }
        if (this._currentPage !== this.state.currentPage) {
            this.setState(
                {
                    currentPage: this._currentPage
                },
                () => {
                    if (this.props.onChange) {
                        this.props.onChange(this._currentPage - 1, isUserSwipe);
                    }
                }
            );
        }
    }

    render() {
        // 更新容器宽度
        this.width = this.props.width;

        let { children, autoHeight, style } = this.props;
        style = style || {};

        return (
            <View
                style={[
                    { width: this.props.width, overflow: 'hidden' },
                    autoHeight || style.flex === 1 ? { flex: 1 } : {}
                ]}
                {...this._panResponder.panHandlers}
            >
                <View
                    ref='scrollPanel'
                    style={[
                        {
                            width: this.width * (this.state.childrenLength + 1),
                            flexDirection: 'row'
                        },
                        autoHeight || style.flex === 1 ? { flex: 1 } : {}
                    ]}
                >
                    {children}
                    {Object.assign({}, children[0], { key: 'carouselKeyLast' })}
                </View>
            </View>
        );
    }
}

module.exports = CarouselPager;
