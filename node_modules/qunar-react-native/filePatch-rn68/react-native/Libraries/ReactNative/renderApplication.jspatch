/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 * @flow
 */

const AppContainer = require('./AppContainer');
import GlobalPerformanceLogger from '../Utilities/GlobalPerformanceLogger';
import type { IPerformanceLogger } from '../Utilities/createPerformanceLogger';
import PerformanceLoggerContext from '../Utilities/PerformanceLoggerContext';
import type { DisplayModeType } from './DisplayMode';
import getCachedComponentWithDebugName from './getCachedComponentWithDebugName';
const React = require('react');

const invariant = require('invariant');
// QRN ADD
const NativeModules = require('../BatchedBridge/NativeModules');
// QRN END


// require BackHandler so it sets the default handler that exits the app if no listeners respond
require('../Utilities/BackHandler');

function renderApplication<Props: Object>(
    RootComponent: React.ComponentType<Props>,
    initialProps: Props,
    rootTag: any,
    WrapperComponent?: ?React.ComponentType<any>,
    fabric?: boolean,
    showArchitectureIndicator?: boolean,
    scopedPerformanceLogger?: IPerformanceLogger,
    isLogBox?: boolean,
    debugName?: string,
    displayMode?: ?DisplayModeType,
    useConcurrentRoot?: boolean,
) {
    invariant(rootTag, 'Expect to have a valid rootTag, instead got ', rootTag);

    const performanceLogger = scopedPerformanceLogger ?? GlobalPerformanceLogger;

    // QRN ADD 检测 Render 是否执行完成, 如超 3 秒, 认为白屏
    let timeout = null,
        qWhiteScreenLayoutCB = null,
        qClearTimeoutCB = null;

    if (!isLogBox) {
        timeout = setTimeout(() => {
            if (global.QrnConfig && global.QrnConfig['whiteScreenErrorReportEnable'] === false) {
                return;
            }

            reportWhiteScreen(debugName);

        }, 3000);

        qWhiteScreenLayoutCB = (e) => {
            if (!e || !e.nativeEvent) {
                return;
            }

            const nativeEvent = e.nativeEvent;
            const { layout = {} } = nativeEvent;
            const { width = 0, height = 0 } = layout;


            if (width && height) {
                timeout && clearTimeout(timeout);
                timeout = null;
            }
        }

        qClearTimeoutCB = () => {
            timeout && clearTimeout(timeout);
            timeout = null;
        }
    }
    // QRN END

    let renderable = (
        <PerformanceLoggerContext.Provider value={performanceLogger}>
            <AppContainer
                rootTag={rootTag}
                fabric={fabric}
                // QRN ADD
                qWhiteScreenLayoutCB={qWhiteScreenLayoutCB}
                qClearTimeoutCB={qClearTimeoutCB}
                // QRN END
                showArchitectureIndicator={showArchitectureIndicator}
                WrapperComponent={WrapperComponent}
                initialProps={initialProps ?? Object.freeze({})}
                internal_excludeLogBox={isLogBox}>
                <RootComponent {...initialProps} rootTag={rootTag} />
            </AppContainer>
        </PerformanceLoggerContext.Provider>
    );

    if (__DEV__ && debugName) {
        const RootComponentWithMeaningfulName = getCachedComponentWithDebugName(
            `${debugName}(RootComponent)`,
        );
        renderable = (
            <RootComponentWithMeaningfulName>
                {renderable}
            </RootComponentWithMeaningfulName>
        );
    }

    // QRN ADD
    reportQrnPages({ debugName }, 'renderApplicationStart');
    // QRN END
    performanceLogger.startTimespan('renderApplication_React_render');
    performanceLogger.setExtra('usedReactFabric', fabric ? '1' : '0');
    if (fabric) {
        require('../Renderer/shims/ReactFabric').render(
            renderable,
            rootTag,
            null,
            useConcurrentRoot,
        );
    } else {
        require('../Renderer/shims/ReactNative').render(renderable, rootTag);
    }
    performanceLogger.stopTimespan('renderApplication_React_render');
    // QRN ADD
    reportQrnPages({ debugName }, 'renderApplicationEnd');
    // QRN END
}

// QRN ADD
// 白屏监控上报
const reportWhiteScreen = (pageName) => {
    const hybridid = global.__QP_INFO && global.__QP_INFO.hybridid;
    const qpVer = global.__QP_INFO && global.__QP_INFO.version;

    const data = {
        ext: {
            hybridId: hybridid,
            pageName
        },
        qpVer: String(qpVer),
        bizTag: 'APP',
        bizType: 'app',
        module: 'default',
        appcode: 'qrn-js',
        page: 'qrnPageWhiteScreenMonitor',
        id: 'whiteScreen',
        operType: 'monitor',
        key: 'app/qrnPageWhiteScreenMonitor/default/monitor/whiteScreen',
        operTime: String(new Date().getTime())
    };

    NativeModules.QAV && NativeModules.QAV.componentLog && NativeModules.QAV.componentLog(data);
};

const reportQrnPages = (ext, id) => {
    const logBaseData = {
        ext,
        bizType: 'app',
        bizTag: 'APP',
        module: 'default',
        appcode: 'qrn_js',
        page: 'qrn_pages',
        id,
        operType: 'enter',
        key: `app/qrn_pages/default/enter/${id}`,
        operTime: String(new Date().getTime())
    };

    NativeModules.QAV && NativeModules.QAV.componentLog && NativeModules.QAV.componentLog(logBaseData);

};
// QRN END

module.exports = renderApplication;
