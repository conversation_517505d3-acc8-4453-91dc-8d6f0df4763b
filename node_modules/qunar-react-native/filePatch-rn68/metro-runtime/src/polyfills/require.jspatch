/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @polyfill
 *
 * @format
 */
'use strict';
/* eslint-disable no-bitwise */

global.__r = metroRequire;
global.__d = define;
global.__c = clear;
global.__registerSegment = registerSegment;

// QRN ADD
const BIZ_MODULE_BASE = 200000;

global.qrnRamPreRequire = qrnRamPreRequire;
global.setPreRequireMaximum = setPreRequireMaximum;
global.qrnRamPreRequireForNative = qrnRamPreRequireForNative;
// QRN END

var modules = clear(); // Don't use a Symbol here, it would pull in an extra polyfill with all sorts of
// additional stuff (e.g. Array.from).

const EMPTY = {};
const { hasOwnProperty } = {};

if (__DEV__) {
    global.$RefreshReg$ = () => {};

    global.$RefreshSig$ = () => (type) => type;
}

function clear() {
    modules = Object.create(null); // We return modules here so that we can assign an initial value to modules
    // when defining it. Otherwise, we would have to do "let modules = null",
    // which will force us to add "nullthrows" everywhere.

    return modules;
}

if (__DEV__) {
    var verboseNamesToModuleIds = Object.create(null);
    var initializingModuleIds = [];
}

// QRN ADD preloadmodule
function setPreRequireMaximum(num) {
    if (typeof num === 'number') {
        global.bizPreRequireMaximum = num;
    }
}

function doLoadQrnModule(moduleId, mapKey, hybridid, maxTime, startTime) {
    // 超过设定的加载时间后, 直接返回
    // if (maxTime && Date.now() - startTime >= maxTime) {
    //     return;
    // }

    let module = modules[moduleId];

    if (!module) {
        metroRequire(moduleId);
        module = modules[moduleId];
    }

    if (!module) {
        return;
    }

    if (!global[mapKey][moduleId]) {
        global[mapKey][moduleId] = 1;
    } else {
        return;
    }

    if (module.dependencyMap && module.dependencyMap.length > 0) {
        let bizModules = module.dependencyMap.filter((mid) => mid >= BIZ_MODULE_BASE);
        // let willLoadArr = bizModules.filter((mid) => !modules[mid]);

        // if (willLoadArr.length) {
        //     // 批量加载模块
        //     qJSLoadScriptFromString(hybridid, willLoadArr);
        // }

        for (const id of bizModules) {
            if (maxTime && Date.now() - startTime >= maxTime) {
                return;
            }

            if (!modules[id]) {
                // qJSLoadScriptFromString(hybridid, id);
                metroRequire(id);
            }

            doLoadQrnModule(id, mapKey, hybridid, maxTime, startTime);
        }
    }
}

function qrnRamPreRequireForNative(pageName, maxTime) {
    if (typeof pageName === 'string' && global.BizPageNameMap) {
        const isRam = IsRamBundle();

        if (!isRam) {
            return;
        }

        const startTime = Date.now(),
            indexName = '__indexPage__';

        let moduleId;

        if (pageName && global.BizPageNameMap[pageName]) {
            moduleId = global.BizPageNameMap[pageName];
        } else if (!pageName && global.BizPageNameMap[indexName]) {
            moduleId = global.BizPageNameMap[indexName];
        } else {
            return;
        }

        const mapKey = '__q_Map_1';

        global[mapKey] = {};

        doLoadQrnModule(moduleId, mapKey, isRam.hybridid, maxTime || null, startTime);

        const endT = Date.now() - startTime;

        // console.log('qrnRamPreRequireForNative 处理个数: ', Object.keys(mapKey).length);
        console.log('qrnRamPreRequireForNative 处理时间: ', endT);

        delete global[mapKey];
    }
}

function IsRamBundle() {
    let hybridid, bundle_type;

    if (global.__QP_INFO) {
        hybridid = global.__QP_INFO.hybridid;
        bundle_type = global.__QP_INFO.bundle_type || '';
    }

    if (bundle_type !== 'ram-bundle') {
        return false;
    }

    return { hybridid };
}
function qrnRamPreRequire(moduleId) {
    if (!moduleId || moduleId < BIZ_MODULE_BASE) {
        // 仅支持业务模块预加载
        return;
    }

    const isRam = IsRamBundle();

    if (!isRam) {
        metroRequire(moduleId);
        return;
    }

    const startTime = Date.now();

    const mapKey = '__q_Map_2';

    if (!global[mapKey]) {
        global[mapKey] = {};
    }

    doLoadQrnModule(
        moduleId,
        mapKey,
        isRam.hybridid,
        global.bizPreRequireMaximum || null,
        startTime
    );
    const endTime = Date.now();

    // console.log('qrnRamPreRequire 处理个数: ', Object.keys(global[mapKey]).length);
    console.log('qrnRamPreRequire 处理时间: ', endTime - startTime);
    delete global[mapKey];

    if (!global._q_ram_bundle_pre_load_time_array) {
        global._q_ram_bundle_pre_load_time_array = [];
    }

    if (global._q_ram_bundle_pre_load_time_array.length < 3) {
        global._q_ram_bundle_pre_load_time_array.push([startTime, endTime]);
    }
}
// QRN END

function define(factory, moduleId, dependencyMap) {
    if (modules[moduleId] != null) {
        if (__DEV__) {
            // (We take `inverseDependencies` from `arguments` to avoid an unused
            // named parameter in `define` in production.
            const inverseDependencies = arguments[4]; // If the module has already been defined and the define method has been
            // called with inverseDependencies, we can hot reload it.

            if (inverseDependencies) {
                global.__accept(moduleId, factory, dependencyMap, inverseDependencies);
            }
        } // prevent repeated calls to `global.nativeRequire` to overwrite modules
        // that are already loaded

        return;
    }

    const mod = {
        dependencyMap,
        factory,
        hasError: false,
        importedAll: EMPTY,
        importedDefault: EMPTY,
        isInitialized: false,
        publicModule: {
            exports: {}
        }
    };
    modules[moduleId] = mod;

    if (__DEV__) {
        // HMR
        mod.hot = createHotReloadingObject(); // DEBUGGABLE MODULES NAMES
        // we take `verboseName` from `arguments` to avoid an unused named parameter
        // in `define` in production.

        const verboseName = arguments[3];

        if (verboseName) {
            mod.verboseName = verboseName;
            verboseNamesToModuleIds[verboseName] = moduleId;
        }
    }
}

function metroRequire(moduleId) {
    if (__DEV__ && typeof moduleId === 'string') {
        const verboseName = moduleId;
        moduleId = verboseNamesToModuleIds[verboseName];

        if (moduleId == null) {
            throw new Error(`Unknown named module: "${verboseName}"`);
        } else {
            console.warn(
                `Requiring module "${verboseName}" by name is only supported for ` +
                    'debugging purposes and will BREAK IN PRODUCTION!'
            );
        }
    } //$FlowFixMe: at this point we know that moduleId is a number

    const moduleIdReallyIsNumber = moduleId;

    if (__DEV__) {
        const initializingIndex = initializingModuleIds.indexOf(moduleIdReallyIsNumber);

        if (initializingIndex !== -1) {
            const cycle = initializingModuleIds
                .slice(initializingIndex)
                .map((id) => (modules[id] ? modules[id].verboseName : '[unknown]')); // We want to show A -> B -> A:

            cycle.push(cycle[0]);
            console.warn(
                `Require cycle: ${cycle.join(' -> ')}\n\n` +
                    'Require cycles are allowed, but can result in uninitialized values. ' +
                    'Consider refactoring to remove the need for a cycle.'
            );
        }
    }

    const module = modules[moduleIdReallyIsNumber];
    return module && module.isInitialized
        ? module.publicModule.exports
        : guardedLoadModule(moduleIdReallyIsNumber, module);
}

function metroImportDefault(moduleId) {
    if (__DEV__ && typeof moduleId === 'string') {
        const verboseName = moduleId;
        moduleId = verboseNamesToModuleIds[verboseName];
    } //$FlowFixMe: at this point we know that moduleId is a number

    const moduleIdReallyIsNumber = moduleId;

    if (
        modules[moduleIdReallyIsNumber] &&
        modules[moduleIdReallyIsNumber].importedDefault !== EMPTY
    ) {
        return modules[moduleIdReallyIsNumber].importedDefault;
    }

    const exports = metroRequire(moduleIdReallyIsNumber);
    const importedDefault = exports && exports.__esModule ? exports.default : exports; // $FlowFixMe The metroRequire call above will throw if modules[id] is null

    return (modules[moduleIdReallyIsNumber].importedDefault = importedDefault);
}

metroRequire.importDefault = metroImportDefault;

function metroImportAll(moduleId) {
    if (__DEV__ && typeof moduleId === 'string') {
        const verboseName = moduleId;
        moduleId = verboseNamesToModuleIds[verboseName];
    } //$FlowFixMe: at this point we know that moduleId is a number

    const moduleIdReallyIsNumber = moduleId;

    if (modules[moduleIdReallyIsNumber] && modules[moduleIdReallyIsNumber].importedAll !== EMPTY) {
        return modules[moduleIdReallyIsNumber].importedAll;
    }

    const exports = metroRequire(moduleIdReallyIsNumber);
    let importedAll;

    if (exports && exports.__esModule) {
        importedAll = exports;
    } else {
        importedAll = {}; // Refrain from using Object.assign, it has to work in ES3 environments.

        if (exports) {
            for (const key in exports) {
                if (hasOwnProperty.call(exports, key)) {
                    importedAll[key] = exports[key];
                }
            }
        }

        importedAll.default = exports;
    } // $FlowFixMe The metroRequire call above will throw if modules[id] is null

    return (modules[moduleIdReallyIsNumber].importedAll = importedAll);
}

metroRequire.importAll = metroImportAll;
let inGuard = false;

function guardedLoadModule(moduleId, module) {
    if (!inGuard && global.ErrorUtils) {
        inGuard = true;
        let returnValue;

        try {
            returnValue = loadModuleImplementation(moduleId, module);
        } catch (e) {
            // TODO: (moti) ********* Type this use of ErrorUtils.
            global.ErrorUtils.reportFatalError(e);
        }

        inGuard = false;
        return returnValue;
    } else {
        return loadModuleImplementation(moduleId, module);
    }
}

const ID_MASK_SHIFT = 16;
const LOCAL_ID_MASK = ~0 >>> ID_MASK_SHIFT;

function unpackModuleId(moduleId) {
    const segmentId = moduleId >>> ID_MASK_SHIFT;
    const localId = moduleId & LOCAL_ID_MASK;
    return {
        segmentId,
        localId
    };
}

metroRequire.unpackModuleId = unpackModuleId;

function packModuleId(value) {
    return (value.segmentId << ID_MASK_SHIFT) + value.localId;
}

metroRequire.packModuleId = packModuleId;
const moduleDefinersBySegmentID = [];
const definingSegmentByModuleID = new Map();

function registerSegment(segmentId, moduleDefiner, moduleIds) {
    moduleDefinersBySegmentID[segmentId] = moduleDefiner;

    if (__DEV__) {
        if (segmentId === 0 && moduleIds) {
            throw new Error('registerSegment: Expected moduleIds to be null for main segment');
        }

        if (segmentId !== 0 && !moduleIds) {
            throw new Error(
                'registerSegment: Expected moduleIds to be passed for segment #' + segmentId
            );
        }
    }

    if (moduleIds) {
        moduleIds.forEach((moduleId) => {
            if (!modules[moduleId] && !definingSegmentByModuleID.has(moduleId)) {
                definingSegmentByModuleID.set(moduleId, segmentId);
            }
        });
    }
}

// QRN ADD
function _qJSLoadScriptFromString(moduleId) {
    const hybridId = global.__QP_INFO && global.__QP_INFO['hybridid'];

    // 0 for success, -1 for hybridId loading failure, -2 for moduleId loading failure
    const resCode = qJSLoadScriptFromString(hybridId, moduleId);
    switch (resCode) {
        case -1:
            console.log('qJSLoadScriptFromString hybridId loading failure');
            break;
        case -2:
            console.log('qJSLoadScriptFromString moduleId loading failure');
            break;
        case -3:
            throw new Error('qJSLoadScriptFromString code -3. hybridId: ' + hybridId);
            break;
        default:
            break;
    }
    return modules[moduleId] || null;
}
// Native 通知 C 层加载拆包模块
function doQNativeRequire() {
    let hybridId = '',
        qpVersion = '';

    if (global.__QP_INFO) {
        hybridId = global.__QP_INFO['hybridid'];
        qpVersion = global.__QP_INFO['version'];
    }

    // 如 jsc 没有准备好模块, 需要通过安卓再通知 jsc
    global.QNativeRequire.requireModules({
        hybridId,
        qpVersion
    });
}
// QRN END

function loadModuleImplementation(moduleId, module) {
    // QRN ADD 业务开始加载的时间
    if (!global.__q_firstModuleLoadTime && moduleId > BIZ_MODULE_BASE) {
        global.__q_firstModuleLoadTime = Date.now();
    }
    // QRN END

    if (!module && moduleDefinersBySegmentID.length > 0) {
        var _definingSegmentByMod;

        const segmentId =
            (_definingSegmentByMod = definingSegmentByModuleID.get(moduleId)) !== null &&
            _definingSegmentByMod !== void 0
                ? _definingSegmentByMod
                : 0;
        const definer = moduleDefinersBySegmentID[segmentId];

        if (definer != null) {
            definer(moduleId);
            module = modules[moduleId];
            definingSegmentByModuleID.delete(moduleId);
        }
    }

    const nativeRequire = global.nativeRequire;

    if (!module && nativeRequire) {
        const { segmentId, localId } = unpackModuleId(moduleId);
        nativeRequire(localId, segmentId);
        module = modules[moduleId];
    }

    // 拆包 逻辑
    if (!module && global.QNativeRequire) {
        // beta code
        // const log_load_script_start = Date.now();

        // 临时修复方案, 先让 Native 通知 C 层加载
        if (!global._hasRunQNativeRequire) {
            // 如 jsc 没有准备好模块, 需要通过安卓再通知 jsc
            doQNativeRequire();

            global._hasRunQNativeRequire = true;
        }

        if (!global._q_ram_bundle_first_load_script_start) {
            global._q_ram_bundle_first_load_script_start = Date.now();
        }

        module = _qJSLoadScriptFromString(moduleId);

        if (!global._q_ram_bundle_first_load_script_end) {
            global._q_ram_bundle_first_load_script_end = Date.now();
        }

        // beta code
        // const log_load_script_end = Date.now();
        // const log_load_script_time = log_load_script_end - log_load_script_start;
        // if (global._q_ram_bundle_load_script_total_time) {
        //     global._q_ram_bundle_load_script_total_time += log_load_script_time;
        // } else {
        //     global._q_ram_bundle_load_script_total_time = log_load_script_time;
        // }
    }

    // 拆包 重试 逻辑
    if (!module && global.QNativeRequire) {
        // 如 jsc 没有准备好模块, 需要通过安卓再通知 jsc
        doQNativeRequire();
        module = _qJSLoadScriptFromString(moduleId);
    }
    // QRN END

    // QRN ADD 记录模块请求 仅 beta 下
    if (global.__q_requireModuleIds) {
        global.__q_requireModuleIds.add(moduleId);
    }
    // QRN END

    if (!module) {
        throw unknownModuleError(moduleId);
    }

    if (module.hasError) {
        throw moduleThrewError(moduleId, module.error);
    }

    if (__DEV__) {
        var Systrace = requireSystrace();
        var Refresh = requireRefresh();
    } // We must optimistically mark module as initialized before running the
    // factory to keep any require cycles inside the factory from causing an
    // infinite require loop.

    module.isInitialized = true;
    const { factory, dependencyMap } = module;

    if (__DEV__) {
        initializingModuleIds.push(moduleId);
    }

    try {
        if (__DEV__) {
            // $FlowIgnore: we know that __DEV__ is const and `Systrace` exists
            Systrace.beginEvent('JS_require_' + (module.verboseName || moduleId));
        }

        const moduleObject = module.publicModule;

        if (__DEV__) {
            moduleObject.hot = module.hot;
            var prevRefreshReg = global.$RefreshReg$;
            var prevRefreshSig = global.$RefreshSig$;

            if (Refresh != null) {
                const RefreshRuntime = Refresh;

                global.$RefreshReg$ = (type, id) => {
                    RefreshRuntime.register(type, moduleId + ' ' + id);
                };

                global.$RefreshSig$ = RefreshRuntime.createSignatureFunctionForTransform;
            }
        }

        moduleObject.id = moduleId; // keep args in sync with with defineModuleCode in
        // metro/src/Resolver/index.js
        // and metro/src/ModuleGraph/worker.js

        factory(
            global,
            metroRequire,
            metroImportDefault,
            metroImportAll,
            moduleObject,
            moduleObject.exports,
            dependencyMap
        ); // avoid removing factory in DEV mode as it breaks HMR

        if (!__DEV__) {
            // $FlowFixMe: This is only sound because we never access `factory` again
            // QRN ADD rn 不需要了, 但是我们JS模块预热需要
            module.factory = undefined;
            // module.dependencyMap = undefined;
            // QRN END
        }

        if (__DEV__) {
            // $FlowIgnore: we know that __DEV__ is const and `Systrace` exists
            Systrace.endEvent();

            if (Refresh != null) {
                registerExportsForReactRefresh(Refresh, moduleObject.exports, moduleId);
            }
        }

        return moduleObject.exports;
    } catch (e) {
        module.hasError = true;
        module.error = e;
        module.isInitialized = false;
        module.publicModule.exports = undefined;
        throw e;
    } finally {
        if (__DEV__) {
            if (initializingModuleIds.pop() !== moduleId) {
                throw new Error('initializingModuleIds is corrupt; something is terribly wrong');
            }

            global.$RefreshReg$ = prevRefreshReg;
            global.$RefreshSig$ = prevRefreshSig;
        }
    }
}

function unknownModuleError(id) {
    let message = 'Requiring unknown module "' + id + '".';

    if (__DEV__) {
        message +=
            ' If you are sure the module exists, try restarting Metro. ' +
            'You may also want to run `yarn` or `npm install`.';
    }

    return Error(message);
}

function moduleThrewError(id, error) {
    const displayName = (__DEV__ && modules[id] && modules[id].verboseName) || id;
    return Error('Requiring module "' + displayName + '", which threw an exception: ' + error);
}

if (__DEV__) {
    metroRequire.Systrace = {
        beginEvent: () => {},
        endEvent: () => {}
    };

    metroRequire.getModules = () => {
        return modules;
    }; // HOT MODULE RELOADING

    var createHotReloadingObject = function () {
        const hot = {
            _acceptCallback: null,
            _disposeCallback: null,
            _didAccept: false,
            accept: (callback) => {
                hot._didAccept = true;
                hot._acceptCallback = callback;
            },
            dispose: (callback) => {
                hot._disposeCallback = callback;
            }
        };
        return hot;
    };

    let reactRefreshTimeout = null;

    const metroHotUpdateModule = function (id, factory, dependencyMap, inverseDependencies) {
        const mod = modules[id];

        if (!mod) {
            if (factory) {
                // New modules are going to be handled by the define() method.
                return;
            }

            throw unknownModuleError(id);
        }

        if (!mod.hasError && !mod.isInitialized) {
            // The module hasn't actually been executed yet,
            // so we can always safely replace it.
            mod.factory = factory;
            mod.dependencyMap = dependencyMap;
            return;
        }

        const Refresh = requireRefresh();
        const refreshBoundaryIDs = new Set(); // In this loop, we will traverse the dependency tree upwards from the
        // changed module. Updates "bubble" up to the closest accepted parent.
        //
        // If we reach the module root and nothing along the way accepted the update,
        // we know hot reload is going to fail. In that case we return false.
        //
        // The main purpose of this loop is to figure out whether it's safe to apply
        // a hot update. It is only safe when the update was accepted somewhere
        // along the way upwards for each of its parent dependency module chains.
        //
        // We perform a topological sort because we may discover the same
        // module more than once in the list of things to re-execute, and
        // we want to execute modules before modules that depend on them.
        //
        // If we didn't have this check, we'd risk re-evaluating modules that
        // have side effects and lead to confusing and meaningless crashes.

        let didBailOut = false;
        const updatedModuleIDs = topologicalSort(
            [id], // Start with the changed module and go upwards
            (pendingID) => {
                const pendingModule = modules[pendingID];

                if (pendingModule == null) {
                    // Nothing to do.
                    return [];
                }

                const pendingHot = pendingModule.hot;

                if (pendingHot == null) {
                    throw new Error('[Refresh] Expected module.hot to always exist in DEV.');
                } // A module can be accepted manually from within itself.

                let canAccept = pendingHot._didAccept;

                if (!canAccept && Refresh != null) {
                    // Or React Refresh may mark it accepted based on exports.
                    const isBoundary = isReactRefreshBoundary(
                        Refresh,
                        pendingModule.publicModule.exports
                    );

                    if (isBoundary) {
                        canAccept = true;
                        refreshBoundaryIDs.add(pendingID);
                    }
                }

                if (canAccept) {
                    // Don't look at parents.
                    return [];
                } // If we bubble through the roof, there is no way to do a hot update.
                // Bail out altogether. This is the failure case.

                const parentIDs = inverseDependencies[pendingID];

                if (parentIDs.length === 0) {
                    // Reload the app because the hot reload can't succeed.
                    // This should work both on web and React Native.
                    performFullRefresh('No root boundary', {
                        source: mod,
                        failed: pendingModule
                    });
                    didBailOut = true;
                    return [];
                } // This module can't handle the update but maybe all its parents can?
                // Put them all in the queue to run the same set of checks.

                return parentIDs;
            },
            () => didBailOut // Should we stop?
        ).reverse();

        if (didBailOut) {
            return;
        } // If we reached here, it is likely that hot reload will be successful.
        // Run the actual factories.

        const seenModuleIDs = new Set();

        for (let i = 0; i < updatedModuleIDs.length; i++) {
            // Don't process twice if we have a cycle.
            const updatedID = updatedModuleIDs[i];

            if (seenModuleIDs.has(updatedID)) {
                continue;
            }

            seenModuleIDs.add(updatedID);
            const updatedMod = modules[updatedID];

            if (updatedMod == null) {
                throw new Error('[Refresh] Expected to find the updated module.');
            }

            const prevExports = updatedMod.publicModule.exports;
            const didError = runUpdatedModule(
                updatedID,
                updatedID === id ? factory : undefined,
                updatedID === id ? dependencyMap : undefined
            );
            const nextExports = updatedMod.publicModule.exports;

            if (didError) {
                // The user was shown a redbox about module initialization.
                // There's nothing for us to do here until it's fixed.
                return;
            }

            if (refreshBoundaryIDs.has(updatedID)) {
                // Since we just executed the code for it, it's possible
                // that the new exports make it ineligible for being a boundary.
                const isNoLongerABoundary = !isReactRefreshBoundary(Refresh, nextExports); // It can also become ineligible if its exports are incompatible
                // with the previous exports.
                // For example, if you add/remove/change exports, we'll want
                // to re-execute the importing modules, and force those components
                // to re-render. Similarly, if you convert a class component
                // to a function, we want to invalidate the boundary.

                const didInvalidate = shouldInvalidateReactRefreshBoundary(
                    Refresh,
                    prevExports,
                    nextExports
                );

                if (isNoLongerABoundary || didInvalidate) {
                    // We'll be conservative. The only case in which we won't do a full
                    // reload is if all parent modules are also refresh boundaries.
                    // In that case we'll add them to the current queue.
                    const parentIDs = inverseDependencies[updatedID];

                    if (parentIDs.length === 0) {
                        // Looks like we bubbled to the root. Can't recover from that.
                        performFullRefresh(
                            isNoLongerABoundary ? 'No longer a boundary' : 'Invalidated boundary',
                            {
                                source: mod,
                                failed: updatedMod
                            }
                        );
                        return;
                    } // Schedule all parent refresh boundaries to re-run in this loop.

                    for (let j = 0; j < parentIDs.length; j++) {
                        const parentID = parentIDs[j];
                        const parentMod = modules[parentID];

                        if (parentMod == null) {
                            throw new Error('[Refresh] Expected to find parent module.');
                        }

                        const canAcceptParent = isReactRefreshBoundary(
                            Refresh,
                            parentMod.publicModule.exports
                        );

                        if (canAcceptParent) {
                            // All parents will have to re-run too.
                            refreshBoundaryIDs.add(parentID);
                            updatedModuleIDs.push(parentID);
                        } else {
                            performFullRefresh('Invalidated boundary', {
                                source: mod,
                                failed: parentMod
                            });
                            return;
                        }
                    }
                }
            }
        }

        if (Refresh != null) {
            // Debounce a little in case there are multiple updates queued up.
            // This is also useful because __accept may be called multiple times.
            if (reactRefreshTimeout == null) {
                reactRefreshTimeout = setTimeout(() => {
                    reactRefreshTimeout = null; // Update React components.

                    Refresh.performReactRefresh();
                }, 30);
            }
        }
    };

    const topologicalSort = function (roots, getEdges, earlyStop) {
        const result = [];
        const visited = new Set();

        function traverseDependentNodes(node) {
            visited.add(node);
            const dependentNodes = getEdges(node);

            if (earlyStop(node)) {
                return;
            }

            dependentNodes.forEach((dependent) => {
                if (visited.has(dependent)) {
                    return;
                }

                traverseDependentNodes(dependent);
            });
            result.push(node);
        }

        roots.forEach((root) => {
            if (!visited.has(root)) {
                traverseDependentNodes(root);
            }
        });
        return result;
    };

    const runUpdatedModule = function (id, factory, dependencyMap) {
        const mod = modules[id];

        if (mod == null) {
            throw new Error('[Refresh] Expected to find the module.');
        }

        const { hot } = mod;

        if (!hot) {
            throw new Error('[Refresh] Expected module.hot to always exist in DEV.');
        }

        if (hot._disposeCallback) {
            try {
                hot._disposeCallback();
            } catch (error) {
                console.error(`Error while calling dispose handler for module ${id}: `, error);
            }
        }

        if (factory) {
            mod.factory = factory;
        }

        if (dependencyMap) {
            mod.dependencyMap = dependencyMap;
        }

        mod.hasError = false;
        mod.error = undefined;
        mod.importedAll = EMPTY;
        mod.importedDefault = EMPTY;
        mod.isInitialized = false;
        const prevExports = mod.publicModule.exports;
        mod.publicModule.exports = {};
        hot._didAccept = false;
        hot._acceptCallback = null;
        hot._disposeCallback = null;
        metroRequire(id);

        if (mod.hasError) {
            // This error has already been reported via a redbox.
            // We know it's likely a typo or some mistake that was just introduced.
            // Our goal now is to keep the rest of the application working so that by
            // the time user fixes the error, the app isn't completely destroyed
            // underneath the redbox. So we'll revert the module object to the last
            // successful export and stop propagating this update.
            mod.hasError = false;
            mod.isInitialized = true;
            mod.error = null;
            mod.publicModule.exports = prevExports; // We errored. Stop the update.

            return true;
        }

        if (hot._acceptCallback) {
            try {
                hot._acceptCallback();
            } catch (error) {
                console.error(`Error while calling accept handler for module ${id}: `, error);
            }
        } // No error.

        return false;
    };

    const performFullRefresh = (reason, modules) => {
        /* global window */
        if (
            typeof window !== 'undefined' &&
            window.location != null &&
            typeof window.location.reload === 'function'
        ) {
            window.location.reload();
        } else {
            const Refresh = requireRefresh();

            if (Refresh != null) {
                var _modules$source$verbo, _modules$source, _modules$failed$verbo, _modules$failed;

                const sourceName =
                    (_modules$source$verbo =
                        (_modules$source = modules.source) === null || _modules$source === void 0
                            ? void 0
                            : _modules$source.verboseName) !== null &&
                    _modules$source$verbo !== void 0
                        ? _modules$source$verbo
                        : 'unknown';
                const failedName =
                    (_modules$failed$verbo =
                        (_modules$failed = modules.failed) === null || _modules$failed === void 0
                            ? void 0
                            : _modules$failed.verboseName) !== null &&
                    _modules$failed$verbo !== void 0
                        ? _modules$failed$verbo
                        : 'unknown';
                Refresh.performFullRefresh(
                    `Fast Refresh - ${reason} <${sourceName}> <${failedName}>`
                );
            } else {
                console.warn('Could not reload the application after an edit.');
            }
        }
    }; // Modules that only export components become React Refresh boundaries.

    var isReactRefreshBoundary = function (Refresh, moduleExports) {
        if (Refresh.isLikelyComponentType(moduleExports)) {
            return true;
        }

        if (moduleExports == null || typeof moduleExports !== 'object') {
            // Exit if we can't iterate over exports.
            return false;
        }

        let hasExports = false;
        let areAllExportsComponents = true;

        for (const key in moduleExports) {
            hasExports = true;

            if (key === '__esModule') {
                continue;
            }

            const desc = Object.getOwnPropertyDescriptor(moduleExports, key);

            if (desc && desc.get) {
                // Don't invoke getters as they may have side effects.
                return false;
            }

            const exportValue = moduleExports[key];

            if (!Refresh.isLikelyComponentType(exportValue)) {
                areAllExportsComponents = false;
            }
        }

        return hasExports && areAllExportsComponents;
    };

    var shouldInvalidateReactRefreshBoundary = (Refresh, prevExports, nextExports) => {
        const prevSignature = getRefreshBoundarySignature(Refresh, prevExports);
        const nextSignature = getRefreshBoundarySignature(Refresh, nextExports);

        if (prevSignature.length !== nextSignature.length) {
            return true;
        }

        for (let i = 0; i < nextSignature.length; i++) {
            if (prevSignature[i] !== nextSignature[i]) {
                return true;
            }
        }

        return false;
    }; // When this signature changes, it's unsafe to stop at this refresh boundary.

    var getRefreshBoundarySignature = (Refresh, moduleExports) => {
        const signature = [];
        signature.push(Refresh.getFamilyByType(moduleExports));

        if (moduleExports == null || typeof moduleExports !== 'object') {
            // Exit if we can't iterate over exports.
            // (This is important for legacy environments.)
            return signature;
        }

        for (const key in moduleExports) {
            if (key === '__esModule') {
                continue;
            }

            const desc = Object.getOwnPropertyDescriptor(moduleExports, key);

            if (desc && desc.get) {
                continue;
            }

            const exportValue = moduleExports[key];
            signature.push(key);
            signature.push(Refresh.getFamilyByType(exportValue));
        }

        return signature;
    };

    var registerExportsForReactRefresh = (Refresh, moduleExports, moduleID) => {
        Refresh.register(moduleExports, moduleID + ' %exports%');

        if (moduleExports == null || typeof moduleExports !== 'object') {
            // Exit if we can't iterate over exports.
            // (This is important for legacy environments.)
            return;
        }

        for (const key in moduleExports) {
            const desc = Object.getOwnPropertyDescriptor(moduleExports, key);

            if (desc && desc.get) {
                // Don't invoke getters as they may have side effects.
                continue;
            }

            const exportValue = moduleExports[key];
            const typeID = moduleID + ' %exports% ' + key;
            Refresh.register(exportValue, typeID);
        }
    };

    global.__accept = metroHotUpdateModule;
}

if (__DEV__) {
    // The metro require polyfill can not have module dependencies.
    // The Systrace and ReactRefresh dependencies are, therefore, made publicly
    // available. Ideally, the dependency would be inversed in a way that
    // Systrace / ReactRefresh could integrate into Metro rather than
    // having to make them publicly available.
    var requireSystrace = function requireSystrace() {
        return global.__SYSTRACE || metroRequire.Systrace;
    };

    var requireRefresh = function requireRefresh() {
        return global.__ReactRefresh || metroRequire.Refresh;
    };
}
