/**
 *
 * @providesModule QAppStoreReview
 */

'use strict';

let QRCTAppStoreReview = require('react-native').NativeModules.QRCTAppStoreReview;

const QAppStoreReview = {
    appStoreReview(callback = () => {}, failCallback = () => {}) {
        if (QRCTAppStoreReview) {
            QRCTAppStoreReview.appStoreReview(callback, failCallback);
        } else {
            failCallback('QAppStoreReview API仅在大客户端环境内可用, 切仅支持 iOS 端');
        }
    },
};

module.exports = QAppStoreReview;
