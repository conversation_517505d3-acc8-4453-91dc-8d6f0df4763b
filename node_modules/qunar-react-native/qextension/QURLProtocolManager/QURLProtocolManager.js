/**
 * @providesModule QURLProtocolManager
 */

'use strict';

import { NativeModules, Platform } from 'react-native';
const QRCTURLProtocolManager = NativeModules.QRNURLProtocolManager;

const QURLProtocolManager = {
    unregisterURLSchemes: function(callback = () => {}, failCallback = () => {}) {
        if (Platform.OS === 'ios') {
            QRCTURLProtocolManager.unregisterURLSchemes(callback, failCallback);
        }
    },
};

module.exports = QURLProtocolManager;