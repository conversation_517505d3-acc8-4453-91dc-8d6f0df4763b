/**
 *
 * @providesModule RiskControlInfo
 */

'use strict';

import { NativeModules } from 'react-native';
const QRCTRiskControlInfo = NativeModules.QRCTRiskControlInfo;

const RiskControlInfo = {

  /**
   * 获取风控信息
   * @param function successCallback 成功请求并获取到信息
   **/
  getRiskControlInfo(successCallback) {
      QRCTRiskControlInfo.getRiskControlInfo(successCallback);
  }

};

if (!QRCTRiskControlInfo) {
    console.warn('QRiskControlInfo API仅在大客户端环境内可用');
    module.exports = null;
} else {
    module.exports = RiskControlInfo;
}
