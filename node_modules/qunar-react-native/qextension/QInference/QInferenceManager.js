"use strict";
const { NativeModules } = require("react-native");
const QRCTInferenceManager = NativeModules.QRCTInferenceManager;

let sessionIndex = 1;

const QInferenceManager = {
  createInferenceSession: function (
    params,
    successCallback = () => { },
    failCallback = () => { }
  ) {
    if (QRCTInferenceManager && QRCTInferenceManager.createInferenceSession) {
      params.sessionId = params.modelHybridId + '_' + sessionIndex++;
      const session = new QInferenceSession(params.sessionId, {});
      QRCTInferenceManager.createInferenceSession(
        params,
        function successCB(result) {
          if (result) {
            successCallback(session);
          } else {
            failCallback({
              code: result && result.code ? result.code : -1,
              msg:
                result && result.msg
                  ? result.msg
                  : "createInferenceSession error",
              data: {},
            });
          }
        },
        failCallback
      );
      return session;
    } else {
      failCallback({
        code: -1,
        msg: "当前客户端版本不支持该功能",
        data: {},
      });
    }
    return null;
  }

  // getInferenceEnvInfo: ()
};

class QInferenceSession {
  constructor(sessionId, options = {}) {
    this.sessionId = sessionId;
  }
  /**
   * 运行推理
   */
  run(params, successCallback = () => { }, failCallback = () => { }) {
    if (!this.sessionId) {
      failCallback({
        code: -1,
        msg: "sessionId is empty or session is destroy",
        data: {},
      });
    }

    if (!params) {
      failCallback({
        code: -1,
        msg: "params is empty",
        data: {},
      });
    }
    params.sessionId = this.sessionId;
    QRCTInferenceManager.run(params, successCallback, failCallback);
  }

  /**
   * 销毁会话
   */
  destroy(successCallback = () => { }, failCallback = () => { }) {
    if (!this.sessionId) {
      failCallback({
        code: -1,
        msg: "sessionId is empty or session is destroy",
        data: {},
      });
    }
    QRCTInferenceManager.destroy({ sessionId: this.sessionId }, successCallback, failCallback);
    console.log("Inference session destroyed");
  }
}

module.exports = QInferenceManager;