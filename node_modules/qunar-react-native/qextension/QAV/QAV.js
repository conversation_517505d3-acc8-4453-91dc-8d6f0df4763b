'use strict';

var QAVModule = require('react-native').NativeModules.QAV;

const QAV = {
    /**
     * @param string tag
     * @param string message
     **/
    log: function (tag, message) {
        if (QAVModule && QAVModule.log) {
            try {
                QAVModule.log(tag, message);
            } catch (e) {}
        }
    },

    /**
     * @param string message
     **/
    logMsg: function (message) {
        if (QAVModule && QAVModule.logMsg) {
            try {
                QAVModule.logMsg(message);
            } catch (e) {}
        }
    },

    /**
     * @param object data
     **/
    componentLog: function (data) {
        if (QAVModule && QAVModule.componentLog) {
            try {
                QAVModule.componentLog(data);
            } catch (e) {}
        }
    },
};

module.exports = QAV;
