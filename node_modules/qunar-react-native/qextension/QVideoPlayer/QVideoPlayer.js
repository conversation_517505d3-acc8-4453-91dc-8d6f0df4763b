import React, {Component} from 'react';
import PropTypes from 'prop-types';
import {StyleSheet, requireNativeComponent, NativeModules, View, ViewPropTypes, Image} from 'react-native';
import resolveAssetSource from 'react-native/Libraries/Image/resolveAssetSource';

const styles = StyleSheet.create({
  base: {
    overflow: 'hidden',
  },
});

class QVideoPlayer extends Component {

  constructor(props) {
    super(props);
    this.state = {
      ...props,
    };
  }

  setNativeProps(props) {
    this.setState(props);
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    return nextProps;
  }

  _onLoadStart = (event) => {
    if (this.props.onLoadStart) {
      this.props.onLoadStart(event.nativeEvent);
    }
  };

  _onLoad = (event) => {
    if (this.props.onLoad) {
      this.props.onLoad(event.nativeEvent);
    }
  };

  _onError = (event) => {
    if (this.props.onError) {
      this.props.onError(event.nativeEvent);
    }
  };

  _onSeek = (event) => {
     if (this.props.onSeek) {
      this.props.onSeek(event.nativeEvent);
    }
  };

  _onEnd = (event) => {
    if (this.props.onEnd) {
      this.props.onEnd(event.nativeEvent);
    }
  };

  
  _onBuffer = (event) => {
    if (this.props.onBuffer) {
      this.props.onBuffer(event.nativeEvent);
    }
  };

  _onClickCancel = (event) => {
    if (this.props.onCancel) {
      this.props.onCancel(event.nativeEvent);
    }
  };

  _assignRoot = (component) => {
    this._root = component;
  };

  render() {
    const source = resolveAssetSource(this.state.source) || {};

    let uri = source.uri || '';
    if (uri && uri.match(/^\//)) {
      uri = `file://${uri}`;
    }

    const isNetwork = !!(uri && uri.match(/^https?:/));
    const isAsset = !!(uri && uri.match(/^(assets-library|ph|file|content|ms-appx|ms-appdata):/));
    const nativeProps = Object.assign({}, this.state);
    Object.assign(nativeProps, {
      style: [styles.base, nativeProps.style],
      src: {
        uri,
        isNetwork,
        isAsset,
        type: source.type || '',
        mainVer: source.mainVer || 0,
        patchVer: source.patchVer || 0,
        thumbnailUseMode: source.thumbnailUseMode || 'onWifiAndWWAN',
        disableCache: source.disableCache || false,
      },
      onVideoLoadStart: this._onLoadStart,
      onVideoLoad: this._onLoad,
      onVideoError: this._onError,
      onVideoSeek: this._onSeek,
      onVideoEnd: this._onEnd,
      onVideoBuffer: this._onBuffer,
      onClickCancel: this._onClickCancel,
    });

    return (
      <QRNVideo
        ref={this._assignRoot}
        {...nativeProps}
      />
    );
  }
}

QVideoPlayer.propTypes = {
  /* Native only */
  src: PropTypes.object,
  onVideoLoadStart: PropTypes.func,
  onVideoLoad: PropTypes.func,
  onVideoBuffer: PropTypes.func,
  onVideoError: PropTypes.func,
  onVideoSeek: PropTypes.func,
  onVideoEnd: PropTypes.func,
  onClickCancel: PropTypes.func,
  paused: PropTypes.bool,
  showCancel: PropTypes.bool,
  /* Wrapper component */
  source: PropTypes.oneOfType([
    PropTypes.shape({
      uri: PropTypes.string
    }),
    // Opaque type returned by require('./video.mp4')
    PropTypes.number
  ]),
  onLoadStart: PropTypes.func,
  onLoad: PropTypes.func,
  onBuffer: PropTypes.func,
  onError: PropTypes.func,
  onCancel: PropTypes.func,
  onSeek: PropTypes.func,
  onEnd: PropTypes.func,
  ...ViewPropTypes,
};

const QRNVideo = requireNativeComponent('QRNVideo', QVideoPlayer, {
  nativeOnly: {
    src: true,
    seek: true,
    fullscreen: true,
  },
});

module.exports = QVideoPlayer;
