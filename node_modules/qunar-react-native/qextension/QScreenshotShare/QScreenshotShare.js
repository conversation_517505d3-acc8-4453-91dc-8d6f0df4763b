/**
 *
 * @providesModule ScreenshotShare
 */

'use strict';

import { NativeModules } from 'react-native';
const QRCTScreenshotShare = NativeModules.QRCTScreenshotShare;

const ISFEEDBACK_PARAMETER = 'isFeedback';
const SCHEME_PARAMETER = 'scheme';
const FEEDBACKSCHEME_PARAMETER = 'feedbackScheme';
const SHAREITEM_PARAMETER = 'shareItems';
const SUCCESSCALLBACK_PARAMETER = 'successCallback';
const ERRORCALLBACK_PARAMETER = 'errorCallback';
const SHARE_TYPE_LINK = 3;//分享链接

const ScreenshotShare = {

  /**
   * 注册监听截屏分享
   * @param scheme 分享的Scheme, 无需拼接协议名，直接写业务Scheme，例如: hotel/main
   **/
  register(scheme) {
    QRCTScreenshotShare.register(scheme);
  }, 

   /* 注册监听截屏反馈
   * @param isFeedback 是否需要反馈按钮
   * @param scheme 分享的Scheme, 无需拼接协议名，直接写业务Scheme，例如: hotel/main
   * @param feedbackScheme 截屏反馈跳转的 scheme
   **/
  registerShareAndFeedback: function(parameter) {

    var isFeedback, scheme, feedbackScheme;

    isFeedback = !!parameter[ISFEEDBACK_PARAMETER];
    scheme = parameter[SCHEME_PARAMETER];
    feedbackScheme = parameter[FEEDBACKSCHEME_PARAMETER];


    if (isFeedback == true) {
      if (!feedbackScheme) {
        if (__DEV__) {
          throw ("如果isFeedback为YES，则feedbackScheme不能为空")
        }
        return;
      }
    }

    QRCTScreenshotShare.registerShareAndFeedback(isFeedback, scheme, feedbackScheme);
  },
  
    /**
   * 注册截屏监听-带预览
   * @param { https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=686545254 } parameter 
   * @param successCallback 成功回调
   * @param errorCallback 错误回调
   */
    registerShareWithPreview: function(parameter, successCallback= () => {}, errorCallback= () => {}) {
      if (!(QRCTScreenshotShare && QRCTScreenshotShare.registerShareWithPreview)) {
        errorCallback('registerShareWithPreview api不存在');
        return;
      }
      if(!parameter || !(typeof parameter === 'object')) {
        errorCallback('registerShareWithPreview parameter 必须是对象且不能为空!');
        return;
      }
      var isFeedback = !!parameter[ISFEEDBACK_PARAMETER];
      var scheme = parameter[SCHEME_PARAMETER];
      var feedbackScheme = parameter[FEEDBACKSCHEME_PARAMETER];
      var shareItems = parameter[SHAREITEM_PARAMETER];
      
      if (isFeedback) {
        if (!feedbackScheme) {
          errorCallback('registerShareWithPreview params feedbackScheme can not be empty when isFeedback is true!');
          return;
        }
      }
  
      if(!shareItems || shareItems.length === 0) {
        errorCallback('registerShareWithPreview params shareItems can not be empty!');
        return;
      }
  
      for(let num = 0 ; num < shareItems.length; num++) {
        var item = shareItems[num];
        if(item.shareItemType === SHARE_TYPE_LINK && !item.miniProgramPreview) {
          errorCallback('registerShareWithPreview params miniProgramPreview can not be empty when shareItemType = 3!');
          return;
        }
      }
  
      try {
        QRCTScreenshotShare.registerShareWithPreview(parameter, successCallback, errorCallback);
      } catch (e) {
        errorCallback(e);
      }
      
    },

  /**
   * 注册截屏监听-无预览，仅支持是否自动保存海报
   * @param { https://wiki.corp.qunar.com/confluence/pages/viewpage.action?pageId=686545254 } parameter 参数同registerShareWithPreview
   * @param successCallback 成功回调
   * @param errorCallback 错误回调
   */
  registerShareWithOnlySave: function(parameter, successCallback= () => {}, errorCallback= () => {}) {
    if (!(QRCTScreenshotShare && QRCTScreenshotShare.registerShareWithOnlySave)) {
      errorCallback('registerShareWithOnlySave api不存在');
      return;
    }
    if(!parameter || !(typeof parameter === 'object')) {
      errorCallback('registerShareWithOnlySave parameter 必须是对象且不能为空!');
      return;
    }

    try {
      QRCTScreenshotShare.registerShareWithOnlySave(parameter, successCallback, errorCallback);
    } catch (e) {
      errorCallback(e);
    }
  },

  /**
   * 取消监听截屏分享
   **/
  unregister() {
    QRCTScreenshotShare.unregister();
  },

  /**
   * 主动截屏分享
   * @param String scheme 分享的Scheme(不会覆盖register()传入的Scheme，仅供本地主动截屏使用), 无需拼接协议名，直接写业务Scheme，例如: hotel/main
   * @param function successCallback 成功截屏并弹出分享回调
   * @param function errorCallback 错误回调
   **/
  screenshotAndShare(scheme, successCallback, errorCallback) {
      QRCTScreenshotShare.screenshotAndShare(scheme, successCallback, errorCallback);
  },

  /**
   * 主动截屏分享和反馈
   * @param String isFeedback 是否要反馈功能
   * @param String scheme 分享的Scheme(不会覆盖register()传入的Scheme，仅供本地主动截屏使用), 无需拼接协议名，直接写业务Scheme，例如: hotel/main
   * @param String feedbackScheme 跳转到截屏反馈的页面 
   * @param function successCallback 成功截屏并弹出视图框的回调
   * @param function errorCallback 错误回调
   **/
  screenshotShareAndFeedback: function(parameter) {
    var isFeedback, scheme, feedbackScheme, successCallback, errorCallback;

    isFeedback = !!parameter[ISFEEDBACK_PARAMETER];
    scheme = parameter[SCHEME_PARAMETER];
    feedbackScheme = parameter[FEEDBACKSCHEME_PARAMETER];
    successCallback = parameter[SUCCESSCALLBACK_PARAMETER];
    errorCallback = parameter[ERRORCALLBACK_PARAMETER];

    if (isFeedback == true) {
      if (!feedbackScheme) {
        if (__DEV__) {
          throw ("如果isFeedback为YES，则feedbackScheme不能为空")
        }
        return;
      }
    }

    QRCTScreenshotShare.screenshotShareAndFeedback(isFeedback, scheme, feedbackScheme, successCallback, errorCallback);
  },
  /**
   * 主动带预览截屏
   * @param String isFeedback 是否要反馈功能
   * @param String scheme 分享的Scheme(不会覆盖register()传入的Scheme，仅供本地主动截屏使用), 无需拼接协议名，直接写业务Scheme，例如: hotel/main
   * @param String feedbackScheme 跳转到截屏反馈的页面 
   * @param function successCallback 成功截屏并弹出视图框的回调
   * @param function errorCallback 错误回调
   **/
  screenshotAndShareWithPreview: function(parameter, successCallback= () => {}, errorCallback= () => {}) {
    if (!(QRCTScreenshotShare && QRCTScreenshotShare.screenshotAndShareWithPreview)) {
      errorCallback('screenshotAndShareWithPreview api不存在');
      return;
    }
    if(!parameter || !(typeof parameter === 'object')) {
      errorCallback('screenshotAndShareWithPreview parameter 必须是对象且不能为空!');
      return;
    }
    var isFeedback = !!parameter[ISFEEDBACK_PARAMETER];
    var scheme = parameter[SCHEME_PARAMETER];
    var feedbackScheme = parameter[FEEDBACKSCHEME_PARAMETER];
    var shareItems = parameter[SHAREITEM_PARAMETER];
    
    if (isFeedback) {
      if (!feedbackScheme) {
        errorCallback('screenshotAndShareWithPreview params feedbackScheme can not be empty when isFeedback is true!');
        return;
      }
    }

    if(!shareItems || shareItems.length === 0) {
      errorCallback('screenshotAndShareWithPreview params shareItems can not be empty!');
      return;
    }

    for(let num = 0 ; num < shareItems.length; num++) {
      var item = shareItems[num];
      if(item.shareItemType === SHARE_TYPE_LINK && !item.miniProgramPreview) {
        errorCallback('screenshotAndShareWithPreview params miniProgramPreview can not be empty when shareItemType = 3!');
        return;
      }
    }

    try {
      QRCTScreenshotShare.screenshotAndShareWithPreview(parameter, successCallback, errorCallback);
    } catch (e) {
      errorCallback(e);
    }
  }

};

if (!QRCTScreenshotShare) {
    console.warn('QScreenshotShare API仅在大客户端环境内可用');
    module.exports = null;
} else {
    module.exports = ScreenshotShare;
}
