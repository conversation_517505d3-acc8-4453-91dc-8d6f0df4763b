const QRCTFoldScreenModule = require('react-native').NativeModules?.QRCTFoldScreenModule;
const QFoldScreenManager = {
    getSplitMode: function (param, callback = () => {}) {
        if (QRCTFoldScreenModule && QRCTFoldScreenModule.getSplitMode) {
          callback && QRCTFoldScreenModule.getSplitMode(param, callback);
        } else {
            let result = { "splitMode": "phone" };
            callback && callback(result);
        }
    }
};

module.exports = QFoldScreenManager;