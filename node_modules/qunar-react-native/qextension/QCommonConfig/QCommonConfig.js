/**
 *
 * @providesModule QCommonConfig
 */

'use strict';

let QRCTJSCommonConfig = require('react-native').NativeModules.QRCTJSCommonConfig;
const { Platform } = require('react-native');

const QCommonConfig = {
    /**
     * 获取某个key对应的开关配置
     **/
    getConfigByKey(params, configCallback = () => {}) {
        QRCTJSCommonConfig && QRCTJSCommonConfig.getConfigByKey(params, configCallback);
    },

    /**
     * 获取某个key对应的开关配置
     **/
    getConfig(key, configCallback = () => {}, failCallback = () => {}) {
        QRCTJSCommonConfig &&
            QRCTJSCommonConfig.getConfigByKey(
                {
                    key,
                },
                (data) => {
                    if (Platform.OS === 'android') {
                        if (!data || !Object.prototype.hasOwnProperty.call(data, key)) {
                            failCallback(new Error(`getConfigByKey error: 不存在 ${key}`));
                            return;
                        }

                        configCallback(data[key]);
                    } else {
                        configCallback(data);
                    }
                }
            );
    },

    /**
     * 获取个性化推荐开关
     **/
    getPersonalConfig(configCallback = () => {}) {
        QRCTJSCommonConfig && QRCTJSCommonConfig.getPersonalConfig(configCallback);
    },

    /**
     * 设置个性化推荐开关
     * @param BOOL personalConfig
     **/
    setPersonalConfig(personalConfig) {
        if (typeof personalConfig !== 'boolean') {
            throw '个性化开关参数必须为boolean格式';
        }
        QRCTJSCommonConfig && QRCTJSCommonConfig.setPersonalConfig(personalConfig);
    },
};

module.exports = QCommonConfig;
