/**
 *
 * @providesModule RCTHotDogNetworkTask
 */

'use strict';
import { DeviceInfo, QPInfoManager, QAV } from 'qunar-react-native';

var QHotDogNetWorkModule = require('react-native').NativeModules.QWSearchNetworkTask;

if (!QHotDogNetWorkModule) {
    if (__DEV__) {
        throw '此模块需要Qunar大客户端的网络请求模块';
    }
}

import RCTDeviceEventEmitter from 'react-native/Libraries/EventEmitter/RCTDeviceEventEmitter.js';

var uniqueId = 1;

const HOTDOG_CALLBACK_NAME = 'RNHotDogCallBack';

const SUCCESS_KEY = 'success';
const FAILED__KEY = 'fail';
const CACHE__KEY = 'cache';
const TYPE_KEY = 'type';
const CANCEL_KEY = 'cancel';
const PARAM_ERROR_CODE = 10004;
const TIMEOUT_ERROR_CODE = 10008;

const CALLBACKID_KEY = 'callBackID';
const RESPONSRE_KEY = 'response';

const PARAM_PL_QPS = 'pl_qps';
const PARAM_HYBIRDIDS = 'pl_qps_hybirdIds';

const SERVICETYPE_PARAMETER = 'serviceType';
const URL_PARAMETER = 'url';
const PARAM_PARAMETER = 'param';
const USERCACHE_PARAMETER = 'useCache';
const CACHEKEY_PARAMETER = 'cacheKey';
const TIMEOUT_PARAMETER = 'timeout';
const SUCCESSCALLBACK_PARAMETER = 'successCallback';
const FAILCALLBACK_PARAMETER = 'failCallback';
const CACHECALLBACK_PARAMETER = 'cacheCallback';
const VERSION_PARAMETER = 'version';
const SERIALIZE_PARAMETER = 'serialize';
const TIMING_KEY = 'timing';
const TIMING_JSREQ_START = 'jsReqStart';
const TIMING_JSREQ_CALLBACK = 'jsReqCallBack';
const USE_SIGNATURE = 'useSignature';
var requestMap = {};

var QHotDogNetWork = {
    postRequest: function (parameter): string {
        if (!parameter) {
            if (__DEV__) {
                throw '请求参数不能为空';
            }
            return;
        }

        var self = this;
        var serviceType,
            url,
            param,
            useCache,
            cacheKey,
            useSignature,
            timeout,
            successCallback,
            failCallback,
            cacheCallback,
            serialize;

        var qpVersion = parameter[VERSION_PARAMETER];

        serviceType = parameter[SERVICETYPE_PARAMETER];
        url = parameter[URL_PARAMETER];
        let { statics = false } = parameter;

        useSignature = parameter[USE_SIGNATURE];

        var orginParam = parameter[PARAM_PARAMETER];

        useCache = !!parameter[USERCACHE_PARAMETER];
        cacheKey = parameter[CACHEKEY_PARAMETER];
        timeout = parameter[TIMEOUT_PARAMETER];

        serialize = true;
        if (parameter[SERIALIZE_PARAMETER] !== undefined) {
            serialize = !!parameter[SERIALIZE_PARAMETER];
        }

        successCallback = parameter[SUCCESSCALLBACK_PARAMETER];
        failCallback = parameter[FAILCALLBACK_PARAMETER];
        cacheCallback = parameter[CACHECALLBACK_PARAMETER];

        if (!url && !serviceType) {
            if (__DEV__) {
                throw 'serviceType和url不能同时为空';
            }
            var errorMap = {};
            errorMap['errorCode'] = PARAM_ERROR_CODE;
            errorMap['reason'] = 'serviceType和url不能同时为空';
            failCallback(errorMap);
            return;
        }

        if (useCache == true) {
            if (!cacheKey) {
                if (__DEV__) {
                    throw '如果useCache为YES，则cacheKey不能为空';
                }
                var errorMap = {};
                errorMap['errorCode'] = PARAM_ERROR_CODE;
                errorMap['reason'] = '如果useCache为YES，则cacheKey不能为空';
                failCallback(errorMap);
                return;
            }
        }

        var callBackID = this.addHotDogObserver(successCallback, failCallback, cacheCallback);
        var hasReturn = false;
        self.addListenerCallBack(
            callBackID,
            function () {
                hasReturn = true;
            },
            statics
        );
        var { version } = typeof __QP_INFO !== 'undefined' ? __QP_INFO : { version: 0 };

        // 有用户设置的用用户设置的
        if (qpVersion) {
            version = qpVersion;
        }

        //克隆出一份新的b参对象,因为后面修改会影响原值
        var tmpParam = Object.assign({}, {}, orginParam);

        function postRequest() {
            if (Object.prototype.toString.call(tmpParam).toLowerCase() === '[object object]') {
                param = JSON.stringify(tmpParam);
            }

            var apmInfo = { jsStartTime: Date.now() };

            var requestMap = {
                serviceType,
                url,
                param,
                useCache,
                cacheKey,
                callBackID,
                version: version + '',
                serialize,
                statics,
                useSignature,
                apmInfo
            };

            // 确保timeout有意义在传递
            if (timeout > 0) {
                requestMap['timeout'] = timeout;
                apmInfo['timeout'] = timeout;
            }

            QHotDogNetWorkModule.postRequestByMap(requestMap);
        }

        //业务需要传过来的 pl_qps_hybirdIds 数组 需要判断是否为数组
        var hybirdIds = parameter[PARAM_HYBIRDIDS];
        if (hybirdIds && Object.prototype.toString.call(hybirdIds).toLowerCase() !== '[object array]') {
            var errorMap = {};
            errorMap['errorCode'] = PARAM_ERROR_CODE;
            errorMap['reason'] = 'pl_qps_hybirdIds must be an array --> pl_qps_hybirdIds:[]';
            failCallback(errorMap);
            return;
        }
        //b参需要的 pl_qps 数组
        var pl_qps = tmpParam[PARAM_PL_QPS];

        if (DeviceInfo.releaseType === 'release') {
            if (pl_qps) {
                // release环境 B参包含pl_qps
                postRequest();
                QAV.log('already add pl_qps', JSON.stringify(parameter));
            } else if (hybirdIds && hybirdIds.length > 0) {
                // release环境B参不包含pl_qps 且 业务传过来的 pl_qps_hybirdIds 不为空
                // release环境B参不包含pl_qps 且 业务传过来的 pl_qps_hybirdIds 不为空
                let promises = [];
                for (let hybirdId of hybirdIds) {
                    let promise = new Promise((resolve, reject) => {
                        QPInfoManager.getQpInfo(
                            hybirdId,
                            (data) => {
                                let qpInfo = {};
                                qpInfo.hybridId = hybirdId;
                                qpInfo.version = data ? data.version + '' : '';
                                resolve(qpInfo);
                            },
                            reject('获取qpInfo失败  请检查hyBirdId是否正确 ' + hybirdIds)
                        );
                    });
                    promises.push(promise);
                }
                Promise.all(promises)
                    .then((data) => {
                        tmpParam.pl_qps = data;
                        postRequest();
                    })
                    .catch((e) => {
                        //获取qpInfo失败
                        QAV.log('getQpInfo error', JSON.stringify(parameter));
                        var errorMap = {};
                        errorMap['errorCode'] = PARAM_ERROR_CODE;
                        errorMap['reason'] = e;
                        failCallback(errorMap);
                    });
            } else {
                //此现象为线上B参不包含pl_qps,业务也没有传 pl_qps_hybirdIds
                postRequest();
            }
        } else {
            //beta环境
            if (!pl_qps) {
                //B参不包含pl_qps,有 pl_qps_hybirdIds 但是为空
                if (hybirdIds && hybirdIds.length == 0) {
                    var errorMap = {};
                    errorMap['errorCode'] = PARAM_ERROR_CODE;
                    errorMap['reason'] = 'pl_qps_hybirdIds 不能为空,表现形式为 pl_qps_hybirdIds:[] ';
                    failCallback(errorMap);
                } else {
                    //没有 pl_qps_hybirdIds 或者 pl_qps_hybirdIds 不为空
                    postRequest();
                }
            } else {
                var errorMap = {};
                errorMap['errorCode'] = PARAM_ERROR_CODE;
                if (hybirdIds && hybirdIds.length == 0) {
                    errorMap['reason'] =
                        '1.pl_qps为框架所有，业务不允许定义该字段\n2.pl_qps_hybirdIds 不能为空,表现形式为 pl_qps_hybirdIds:[] ';
                } else {
                    errorMap['reason'] = 'pl_qps为框架所有，业务不允许定义该字段';
                }
                failCallback(errorMap);
            }
        }

        if (timeout > 0) {
            setTimeout(function () {
                if (!hasReturn) {
                    var errorMap = {};
                    errorMap['errorCode'] = TIMEOUT_ERROR_CODE;
                    errorMap['reason'] = '请求超时';
                    self.cancelNetWorkTaskWithParam({ requestId: callBackID, isTimeout: true });
                    failCallback(errorMap);
                }
            }, timeout);
        }
        return callBackID;
    },
    cancelNetWorkTask: function (requestID) {
        QHotDogNetWorkModule && QHotDogNetWorkModule.cancelNetworkTask(requestID)
    },
    cancelNetWorkTaskWithParam: function (param) {
        QHotDogNetWorkModule && QHotDogNetWorkModule.cancelNetWorkTaskWithParam(param);
    },
    addHotDogObserver: function (success: Function, fail: Function, cache: Function): string {
        var callbackId = 'cb_' + uniqueId++ + '_' + (Math.random() + '').replace(/\D/g, '');
        var functionMap = {};
        functionMap[SUCCESS_KEY] = success;
        functionMap[FAILED__KEY] = fail;
        functionMap[CACHE__KEY] = cache;

        requestMap[callbackId] = functionMap;

        return callbackId;
    },
    removeHotDogObserver: function (callbackId) {
        delete requestMap[callbackId];
    },
    addListenerCallBack: function (callbackId, cb, statics = false) {
        var startTimestamp = new Date().getTime().toString();
        ///处理回调
        const currentListener = RCTDeviceEventEmitter.addListener(HOTDOG_CALLBACK_NAME, (dict) => {
            var callbackIdString = dict[CALLBACKID_KEY];
            var type = dict[TYPE_KEY];
            if (callbackIdString == callbackId) {
                // console.log("移除监听者");
                cb && cb();
                var endTimestamp = new Date().getTime().toString();
                var response = dict[RESPONSRE_KEY];

                let timing;
                if (statics) {
                    timing = dict[TIMING_KEY];
                    // timing不为空
                    if (Object.prototype.toString.call(timing).toLowerCase() === '[object object]') {
                        timing[TIMING_JSREQ_START] = startTimestamp;
                        timing[TIMING_JSREQ_CALLBACK] = endTimestamp;
                    }
                }

                var callbackFuncs = requestMap[callbackIdString];
                if (!callbackFuncs) {
                    return;
                }

                switch (type) {
                    case SUCCESS_KEY:
                        {
                            // response 为空时走fail
                            if (response === undefined) {
                                var rnHotdogFailCallback = callbackFuncs[FAILED__KEY];
                                if (rnHotdogFailCallback) {
                                    rnHotdogFailCallback(response, timing);
                                }
                            } else {
                                var rnHotdogSuccessCallback = callbackFuncs[SUCCESS_KEY];
                                if (rnHotdogSuccessCallback) {
                                    rnHotdogSuccessCallback(response, timing);
                                }
                            }

                            // RCTDeviceEventEmitter.removeSubscription(currentListener);
                            currentListener && currentListener.remove();
                            this.removeHotDogObserver(callbackId);
                        }
                        break;
                    case FAILED__KEY:
                        {
                            var rnHotdogFailCallback = callbackFuncs[FAILED__KEY];
                            if (rnHotdogFailCallback) {
                                rnHotdogFailCallback(response, timing);
                            }
                            // RCTDeviceEventEmitter.removeSubscription(currentListener);
                            currentListener && currentListener.remove();
                            this.removeHotDogObserver(callbackId);
                        }
                        break;
                    case CANCEL_KEY:
                        {
                            // RCTDeviceEventEmitter.removeSubscription(currentListener);
                            currentListener && currentListener.remove();
                            this.removeHotDogObserver(callbackId);
                        }
                        break;
                    case CACHE__KEY:
                        {
                            // response 为空时走fail
                            if (response === undefined) {
                                var rnHotdogFailCallback = callbackFuncs[FAILED__KEY];
                                if (rnHotdogFailCallback) {
                                    rnHotdogFailCallback(response, timing);
                                }
                            } else {
                                var rnHotdogCacheCallback = callbackFuncs[CACHE__KEY];
                                if (rnHotdogCacheCallback) {
                                    rnHotdogCacheCallback(response, timing);
                                }
                            }
                        }
                        break;
                    default:
                }
            }
        });
    },
};

module.exports = QHotDogNetWork;
