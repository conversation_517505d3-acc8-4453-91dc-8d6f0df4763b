'use strict';

const QRCTLoadingManager = require('react-native').NativeModules.QRCTLoadingManager;
/**
 * Qunar骆驼加载
 */
const QLoadingManager = {
    startLoadingView: function () {
        if (QRCTLoadingManager && QRCTLoadingManager.startLoadingView) {
            try {
                QRCTLoadingManager.startLoadingView();
            } catch (e) {}
        }
    },
    endLoadingView: function () {
        if (QRCTLoadingManager && QRCTLoadingManager.endLoadingView) {
            try {
                QRCTLoadingManager.endLoadingView();
            } catch (e) {}
        }
    },
};

module.exports = QLoadingManager;
