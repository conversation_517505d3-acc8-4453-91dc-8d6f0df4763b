/**
 * QRN 业务内嵌问卷API
 * @providesModule QRNUrs
 */

'use strict';
const { NativeModules, DeviceEventEmitter } = require('react-native');
const { QRNUrs, QAV } = NativeModules;

// 事件监听缓存
const eventShowListeners = {},
    eventCloseListeners = {},
    eventSubmitListeners = {};

// 展示 事件监听
DeviceEventEmitter.addListener('app-framework-onUrsShow', (opts) => {
    const { resourceId, info } = opts;

    if (!resourceId) {
        return;
    }

    // 上报Urs埋点
    qavShow(opts);

    const listener = eventShowListeners[resourceId];

    if (listener) {
        listener(info);
    }
});

// 关闭 事件监听
DeviceEventEmitter.addListener('app-framework-onClose', (opts) => {
    const { resourceId, info } = opts;

    if (!resourceId) {
        return;
    }

    // 上报Urs埋点
    qavShow(opts);

    const listener = eventCloseListeners[resourceId];

    if (listener) {
        listener(info);
    }
});

// 提交 事件监听
DeviceEventEmitter.addListener('app-framework-onSubmit', (opts) => {
    const { resourceId, info } = opts;

    if (!resourceId) {
        return;
    }

    // 上报Urs埋点
    qavShow(opts);

    const listener = eventSubmitListeners[resourceId];

    if (listener) {
        listener(info);
    }
});

class UrsListener {
    listener = null;
    resourceId = '';
    remove() {
        if (!this.resourceId) {
            throw new Error('resourceId为必传参数, 请检查参数是否正确');
        }

        if (!this.listener) {
            return;
        }

        delete this.listener[this.resourceId];
    }
}

const QUrs = {
    /**
     * 获取Urs问卷数据
     * @param params 请求参数
     * @param callback 返回数据
     */
    getUrs: function (params, callback = () => {}) {
        if (QRNUrs && QRNUrs.getUrs) {
            QRNUrs.getUrs(params, callback);
        }
    },

    /**
     * 通过 taskID 获取Urs问卷数据
     * @param params 请求参数
     * @param callback 返回数据
     */
    getBizUrsData: function (params, callback = () => {}) {
        if (QRNUrs && QRNUrs.getBizUrsData) {
            QRNUrs.getBizUrsData(params, callback);
        }
    },

    /**
     * 展示问卷
     * @param params 请求参数
     * @param callback 返回数据
     */
    onUrsShow: function (params, callback = () => {}) {
        if (QRNUrs && QRNUrs.onUrsShow) {
            QRNUrs.onUrsShow(params, callback);
        }
    },

    /**
     * 提交问卷
     * @param params 请求参数
     * @param callback 返回数据
     */
    onUrsSubmit: function (params, callback = () => {}) {
        if (QRNUrs && QRNUrs.onUrsSubmit) {
            QRNUrs.onUrsSubmit(params, callback);
        }
    },

    /**
     * 调用浮层Urs展示
     *
     * @param params
     * @param callback
     */
    tryShowFloatUrs: function (params, callback = () => {}) {
        if (QRNUrs && QRNUrs.tryShowFloatUrs) {
            QRNUrs.tryShowFloatUrs(params, callback);
        }
    },

    /**
     * 关闭浮层Urs展示
     *
     * @param params
     * @param callback
     */
    closeFloatUrs: function (params, callback = () => {}) {
        if (QRNUrs && QRNUrs.closeFloatUrs) {
            QRNUrs.closeFloatUrs(params, callback);
        }
    },

    /**
     * 控制当前页面浮层Urs展示开关
     *
     * @param params canShow: true/false
     * @param callback
     */
    canShowFloatUrsView: function (params, callback = () => {}) {
        if (QRNUrs && QRNUrs.canShowFloatUrsView) {
            QRNUrs.canShowFloatUrsView(params, callback);
        }
    },
    /**
     * 通道能力建设，urs增加上行行为用户上传
     * @param rootTag 当前页面的rootTag，可以从index.js的initProps中获取
     * @param params canShow: true/false
     * @param callback
     */
    publishAction: function (rootTag = -1, actionCode = "", message = {}, callback = () => { }) {
        if (QRNUrs && QRNUrs.publishAction) {
            QRNUrs.publishAction(rootTag, actionCode, message, callback);
        }
    },

    /**
     * 添加 Urs 问卷展示监听
     *
     * @param resourceId
     * @param callback
     */
    addUrsShowListener: function (resourceId, callback = () => {}) {
        if (!resourceId) {
            throw new Error('resourceId为必传参数, 请检查参数是否正确');
        }
        eventShowListeners[resourceId] = callback;

        const listener = new UrsListener();
        listener.listener = eventShowListeners;
        listener.resourceId = resourceId;

        return listener;
    },

    /**
     * 移除 Urs 问卷展示监听
     *
     * @param resourceId
     */
    removeUrsShowListener: function (resourceId) {
        delete eventShowListeners[resourceId];
    },

    /**
     * 添加 Urs 问卷关闭监听
     *
     * @param resourceId
     * @param callback
     */
    addUrsCloseListener: function (resourceId, callback = () => {}) {
        if (!resourceId) {
            throw new Error('resourceId为必传参数, 请检查参数是否正确');
        }

        eventCloseListeners[resourceId] = callback;

        const listener = new UrsListener();
        listener.listener = eventCloseListeners;
        listener.resourceId = resourceId;

        return listener;
    },

    /**
     * 移除 Urs 问卷关闭监听
     *
     * @param resourceId
     */
    removeUrsCloseListener: function (resourceId) {
        if (!resourceId) {
            return;
        }

        delete eventCloseListeners[resourceId];
    },

    /**
     * 添加 Urs 问卷提交监听
     *
     * @param resourceId
     * @param callback
     */
    addUrsSubmitListener: function (resourceId, callback = () => {}) {
        if (!resourceId) {
            throw new Error('resourceId为必传参数, 请检查参数是否正确');
        }

        eventSubmitListeners[resourceId] = callback;

        const listener = new UrsListener();
        listener.listener = eventSubmitListeners;
        listener.resourceId = resourceId;

        return listener;
    },

    /**
     * 移除 Urs 问卷提交监听
     *
     * @param resourceId
     */
    removeUrsSubmitListener: function (resourceId) {
        if (!resourceId) {
            return;
        }

        delete eventSubmitListeners[resourceId];
    },
    /**
     * 增加urs-module注册，业务可以通过长连接接受urs数据
     * @param {*} rootTag 
     * @param params 注册参数
     * @param {*} callback 
     */
    registerModule: function (rootTag = -1, param = {}, callback = () => { }) {
        if (QRNUrs && QRNUrs.registerModule) {
            QRNUrs.registerModule(rootTag, param, callback);
        }
    },
    /**
     * 注销urs-module注册
     * @param {*} rootTag 
     * @param {*} pageId 
     * @param {*} pageUUID 
     * 
     */
    unRegisterModule: function (rootTag = -1, pageId = "", pageUUID = "", callback = () => { }) {
        if (QRNUrs && QRNUrs.unRegisterModule) {
            QRNUrs.unRegisterModule(rootTag, pageId, pageUUID, callback);
        }
    },
    /**
  * 立即展示URS问卷
  * @pageID {*} pageID
  * @pageID {*} taskID
  * @pageID {*} param
  *
  */
    showFloatUrsImmediately: function (param, callback = () => { }) {
        if (QRNUrs && QRNUrs.showFloatUrsImmediately) {
            QRNUrs.showFloatUrsImmediately(param, callback);
        }
    }
};

// 上报Urs埋点
function qavShow(params) {
    const { resourceId = '', info = '' } = params;

    const data = {
        ext: {
            resourceId,
            info,
            from: 'qrn-js'
        },
        bizType: 'app',
        bizTag: 'APP',
        module: 'default',
        appcode: 'urs',
        page: 'diaochawenjua',
        id: 'resourceId',
        operType: 'show',
        key: 'app/diaochawenjua/default/show/resourceId',
        operTime: '*'
    };

    QAV && QAV.componentLog(data);
}

module.exports = QUrs;
