const ExceptionsManager = require('react-native/Libraries/Core/ExceptionsManager');

function handleException(error, fatal = false) {
    // 仅支持 Error 类型和 string 类型, 其他格式的数据会被忽略
    let err = error;
    if (typeof error === 'string') {
        err = new Error(error);
    } else if (!(error instanceof Error)) {
        return;
    }

    ExceptionsManager.handleException(err, fatal);
}

module.exports = {
    handleException
};
