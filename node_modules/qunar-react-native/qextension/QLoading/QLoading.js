/**
 *
 * @providesModule QLoading
 */

'use strict';

import React, {Component} from 'react';
import PropTypes from 'prop-types';
import {StyleSheet, View, Text, requireNativeComponent} from 'react-native';

/**
 * Qunar骆驼加载组件
 *
 * @component QLoading
 * @version >=v2.2.0
 * @description 渲染出一个骆驼在走动的loading组件,支持富文本自定义。
 *
 * ![QLoadingError](./images/component-QLoading.png)
 */
export default class QLoading extends Component {
  static propTypes = {
    /**
     * 隐藏文字
     *
     * @property hideText
     * @type bool
     * @default false
     * @description 是否隐藏骆驼下面的文字，默认为false不隐藏
     */
    hideText: PropTypes.bool,
    /**
     * 文字内容
     *
     * @property text
     * @type string | array
     * @default ['努力加载中...', '疯狂加载中...', '拼命加载中...'],
     * @description 骆驼下面显示的文字，默认为开始2500ms『努力加载中...』，接下来3000ms『疯狂加载中...』，最终『拼命加载中...』。另外，如果传入字符串，则一直显示该字符串。
     */
    text: PropTypes.oneOfType([PropTypes.string, PropTypes.array])
  };

  static defaultProps = {
    hideText: false,
    text: []
  };
  constructor(props) {
    super(props);

    let {text} = props;
    let step1Text = '努力加载中...';
    let step2Text = '疯狂加载中...';
    let step3Text = '拼命加载中...';
    if (Array.isArray(text)) {
      const [text1,
        text2,
        text3] = text;
      step1Text = text1 || step1Text;
      step2Text = text2 || step2Text;
      step3Text = text3 || step3Text;
    } else {
      step1Text = text;
    }

    this.state = {
      step1Text,
      step2Text,
      step3Text,
      loadHintText: step1Text
    }
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    if (nextProps.text !== prevState.prevText) {
      const { text } = nextProps;
      let step1Text = '努力加载中...';
      let step2Text = '疯狂加载中...';
      let step3Text = '拼命加载中...';

      if (Array.isArray(text)) {
        const [text1, text2, text3] = text;

        step1Text = text1 || step1Text;
        step2Text = text2 || step2Text;
        step3Text = text3 || step3Text;
      } else {
        step1Text = text;
      }

      return { step1Text, step2Text, step3Text, prevText: text };
    }

    return null;
  }

  _onLoadingStateChange(info) {
    switch (info.nativeEvent.state) {
      case 'step1':
        this.setState({loadHintText: this.state.step1Text});
        break;

      case 'step2':
        this.setState({loadHintText: this.state.step2Text});
        break;

      case 'step3':
        this.setState({loadHintText: this.state.step3Text});
        break;

      default:
        break;
    }
  };
  render() {
    let {
      style = {},
      text,
      hideText
    } = this.props;

    // 防止字符串false，android问题
    hideText = ('' + hideText).toLowerCase() === 'false'
      ? false
      : true;

    return (
      <View style={[styles.container, style]}>
        <QLoadingView
          onLoadingStateChange={this
          ._onLoadingStateChange
          .bind(this)}
          style={{

          width: 164,
          height: 104,
          marginTop: 5,
        }}/>
          <Text style ={styles.content}
          numberOfLines={3}>
            {hideText ? '' : this.state.loadHintText}
          </Text>
      </View>
    )
  }
}

const styles = StyleSheet.create({
  container: {
    // justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
    overflow: 'hidden',
    height: 164,
  },
  content: {
    fontSize: 12,
    color: '#999999',
    marginTop: 0,
    width: 140,
    textAlign: 'center',
  }
});

var QLoadingView = requireNativeComponent('QLoadingView', QLoading);

if (!QLoadingView) {
  console.warn('QLoadingView API仅在大客户端环境内可用');
  throw('请更新你的Native包');
  module.exports = null;
} else {
  module.exports = QLoading;
}
