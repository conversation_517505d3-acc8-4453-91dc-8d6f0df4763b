/**
 *
 * @providesModule QRecoveryData
 */

'use strict';
const internalKey = '__q_recovery_data';

const QRecoveryData = {
    getRecoveryData(props) {
        if (props && props[internalKey]) {
            return props[internalKey];
        }

        return '';
    },

    isRecoveryMode(props) {
        if (props && props.hasOwnProperty(internalKey)) {
            return true;
        }

        return false;
    }
};

module.exports = QRecoveryData;
