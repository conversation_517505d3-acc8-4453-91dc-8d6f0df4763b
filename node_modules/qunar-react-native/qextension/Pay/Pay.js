/**
 *
 * @providesModule Pay
 */

"use strict";
// const schemeProtocol = require('qunar-react-native').DeviceInfo.scheme;
const { DeviceInfo } = require("qunar-react-native");
import QSchemeForResult from "../QSchemeForResult/QSchemeForResult";

const Pay = {
  middlePay(url, payResultCallback = () => {}, failCallback = () => {}) {
    try {
      const scheme =
        DeviceInfo.scheme +
        `://pay/middlePay?payUrl=${encodeURIComponent(url)}`;
      const callback = (data) => {
        if (!data.ret) {
          failCallback(new Error(`url跳转失败:${data.errMsg}`));
          return;
        }
        payResultCallback(data.data);
      };
      QSchemeForResult.schemeForResult(
        scheme,
        DeviceInfo.isAndroid ? { useAction: true } : null,
        "",
        callback
      );
    } catch (err) {
      failCallback(new Error(`url跳转失败:${err}`));
    }
  },
  
  openQuickPay(url, payResultCallback = () => {}, failCallback = () => {}) {
    try {
      const scheme =
        DeviceInfo.scheme +
        `://tripPay/openQuickPay?payUrl=${encodeURIComponent(url)}`;
      const callback = (data) => {
        if (!data.ret) {
          failCallback(new Error(`url跳转失败:${data.errMsg}`));
          return;
        }
        payResultCallback(data.data);
      };
      QSchemeForResult.schemeForResult(
        scheme,
        DeviceInfo.isAndroid ? { useAction: true } : null,
        "",
        callback
      );
    } catch (err) {
      failCallback(new Error(`url跳转失败:${err}`));
    }
  },
};

module.exports = Pay;
