/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 * @flow strict-local
 */

import codegenNativeCommands from 'react-native/Libraries/Utilities/codegenNativeCommands';

interface NativeCommands {
    +animateToRegion: (viewRef: React.ElementRef<ComponentType>) => void;
    +animateToCoordinate: (viewRef: React.ElementRef<ComponentType>) => void;
    +fitToSuppliedMarkers: (viewRef: React.ElementRef<ComponentType>) => void;
    +fitToCoordinates: (viewRef: React.ElementRef<ComponentType>) => void;
    +drawRoute: (viewRef: React.ElementRef<ComponentType>) => void;
    +clearAllRoutes: (viewRef: React.ElementRef<ComponentType>) => void;
}

export default (codegenNativeCommands<NativeCommands>({
    supportedCommands: [
        'pointForCoordinate',
        'animateToRegion',
        'animateToCoordinate',
        'fitToSuppliedMarkers',
        'fitToCoordinates',
        'drawRoute',
        'clearAllRoutes',
    ],
}): NativeCommands);
