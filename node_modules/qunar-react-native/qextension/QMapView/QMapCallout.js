/**
 *
 * @providesModule QMapCallout
 */
import PropTypes from 'prop-types';
import React from 'react';
import {
  StyleSheet,
  ViewPropTypes,
  requireNativeComponent
} from 'react-native';

const propTypes = {
  ...ViewPropTypes,
  tooltip: PropTypes.bool,
  onPress: PropTypes.func,
};

class QMapCallout extends React.Component {
  constructor(props) {
    super(props);
    this._onPress = this._onPress.bind(this);
  }

  _onPress(event) {
    if (this.props.onPress) {
      this.props.onPress(event.nativeEvent);
    }
  }

  render() {
    return <QRNMapCallout 
              {...this.props} style={[styles.callout, this.props.style]} 
              onPress = {this._onPress}/>;
  }
}

QMapCallout.propTypes = propTypes;
const QRNMapCallout = requireNativeComponent('QMapCallout', QMapCallout);

const styles = StyleSheet.create({
  callout: {
    position: 'absolute',
  },
});

module.exports = QMapCallout;
