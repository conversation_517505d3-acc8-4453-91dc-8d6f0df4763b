/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 * @flow strict-local
 */

import codegenNativeCommands from 'react-native/Libraries/Utilities/codegenNativeCommands';

interface NativeCommands {
    +selectCallout: (viewRef: React.ElementRef<ComponentType>) => void;
    +deselectCallout: (viewRef: React.ElementRef<ComponentType>) => void;
    +showCallout: (viewRef: React.ElementRef<ComponentType>) => void;
    +hideCallout: (viewRef: React.ElementRef<ComponentType>) => void;
    +updateMarker: (viewRef: React.ElementRef<ComponentType>) => void;
}

export default (codegenNativeCommands<NativeCommands>({
    supportedCommands: ['selectCallout', 'deselectCallout', 'showCallout', 'hideCallout', 'updateMarker'],
}): NativeCommands);
