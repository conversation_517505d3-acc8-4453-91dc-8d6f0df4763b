/**
 *
 * @providesModule QMapView
 */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { requireNativeComponent, NativeModules, ViewPropTypes, findNodeHandle, Platform } from 'react-native';
import MapMarker from './QMapMarker';
import Commands from './QMapViewCommands';

const propTypes = {
    ...ViewPropTypes,
    /**
     * Used to style and layout the `MapView`.  See `StyleSheet.js` and
     * `ViewStylePropTypes.js` for more info.
     */
    style: ViewPropTypes.style,

    /**
     * A json object that describes the style of the map. This is transformed to a string
     * and saved in mayStyleString to be sent to android and ios
     * https://developers.google.com/maps/documentation/ios-sdk/styling#use_a_string_resource
     * https://developers.google.com/maps/documentation/android-api/styling
     */
    // customMapStyle: PropTypes.array,

    /**
     * A json string that describes the style of the map
     * https://developers.google.com/maps/documentation/ios-sdk/styling#use_a_string_resource
     * https://developers.google.com/maps/documentation/android-api/styling
     */
    // customMapStyleString: PropTypes.string,

    /**
     * If `true` the app will ask for the user's location.
     * Default value is `false`.
     *
     * **NOTE**: You need to add NSLocationWhenInUseUsageDescription key in
     * Info.plist to enable geolocation, otherwise it is going
     * to *fail silently*! You will also need to add an explanation for why
     * you need the users location against `NSLocationWhenInUseUsageDescription` in Info.plist.
     * Otherwise Apple may reject your app submission.
     */
    showsUserLocation: PropTypes.bool,

    customStyle: PropTypes.bool,

    /**
     * The title of the annotation for current user location. This only works if
     * `showsUserLocation` is true.
     * There is a default value `My Location` set by MapView.
     *
     * @platform ios
     */
    // userLocationAnnotationTitle: PropTypes.string,

    /**
     * If `false` hide the button to move map to the current user's location.
     * Default value is `true`.
     *
     * @platform android
     */
    // showsMyLocationButton: PropTypes.bool,

    /**
     * If `true` the map will focus on the user's location. This only works if
     * `showsUserLocation` is true and the user has shared their location.
     * Default value is `false`.
     *
     * @platform ios
     */
    // followsUserLocation: PropTypes.bool,

    /**
     * If `false` points of interest won't be displayed on the map.
     * Default value is `true`.
     *
     */
    // showsPointsOfInterest: PropTypes.bool,

    /**
     * If `false` compass won't be displayed on the map.
     * Default value is `true`.
     *
     * @platform ios
     */
    showsCompass: PropTypes.bool,

    /**
     * If `false` the user won't be able to pinch/zoom the map.
     * Default value is `true`.
     *
     */
    zoomEnabled: PropTypes.bool,

    /**
     *If `false` the user won't be able to zoom the map
     * Default value is `true`.
     *
     *@platform android
     */
    // zoomControlEnabled: PropTypes.bool,

    /**
     * If `false` the user won't be able to pinch/rotate the map.
     * Default value is `true`.
     *
     */
    rotateEnabled: PropTypes.bool,

    /**
     * If `true` the map will be cached to an Image for performance
     * Default value is `false`.
     *
     */
    // cacheEnabled: PropTypes.bool,

    /**
     * If `false` the user won't be able to change the map region being displayed.
     * Default value is `true`.
     *
     */
    scrollEnabled: PropTypes.bool,

    /**
     * If `false` the user won't be able to adjust the camera’s pitch angle.
     * Default value is `true`.
     *
     */
    // pitchEnabled: PropTypes.bool,

    /**
     * A Boolean indicating whether on marker press the map will move to the pressed marker
     * Default value is `true`
     *
     * @platform android
     */
    // moveOnMarkerPress: PropTypes.bool,

    /**
     * A Boolean indicating whether the map displays extruded building information.
     * Default value is `true`.
     */
    showsBuildings: PropTypes.bool,

    /**
     * A Boolean value indicating whether the map displays traffic information.
     * Default value is `false`.
     */
    showsTraffic: PropTypes.bool,

    /**
     * A Boolean indicating whether indoor maps should be enabled.
     * Default value is `false`
     *
     * @platform android
     */
    showsIndoors: PropTypes.bool,

    /**
     * The region to be displayed by the map.
     *
     * The region is defined by the center coordinates and the span of
     * coordinates to display.
     */
    region: PropTypes.shape({
        /**
         * Coordinates for the center of the map.
         */
        latitude: PropTypes.number.isRequired,
        longitude: PropTypes.number.isRequired,

        /**
         * Difference between the minimun and the maximum latitude/longitude
         * to be displayed.
         */
        latitudeDelta: PropTypes.number.isRequired,
        longitudeDelta: PropTypes.number.isRequired,
    }),

    /**
     * The initial region to be displayed by the map.  Use this prop instead of `region`
     * only if you don't want to control the viewport of the map besides the initial region.
     *
     * Changing this prop after the component has mounted will not result in a region change.
     *
     * This is similar to the `initialValue` prop of a text input.
     */
    initialRegion: PropTypes.shape({
        /**
         * Coordinates for the center of the map.
         */
        latitude: PropTypes.number.isRequired,
        longitude: PropTypes.number.isRequired,

        /**
         * Difference between the minimun and the maximum latitude/longitude
         * to be displayed.
         */
        latitudeDelta: PropTypes.number.isRequired,
        longitudeDelta: PropTypes.number.isRequired,
    }),
    /**
     * 百度地图自定义样式
     */
     qunarCustomStyle: PropTypes.shape({
         styleId: PropTypes.string,
         sourceName: PropTypes.string,
     }),

    /**
     * The coordinateType of map.  E.X. GCJ02、BD09
     *
     *
     **/
    coordinateType: PropTypes.string,

    /**
     * (Google Maps only)
     *
     * Padding that is used by the Google Map View to position
     * the camera, legal labels and buttons
     *
     */
    // mapPadding: EdgeInsetsPropType,

    /**
     * (Google Maps only, iOS)
     *
     * Whether the safe area padding is added to the Google Map View padding.
     * This affects where markers, compass, Google logo etc. are placed on the view.
     *
     */
    // paddingAdjustmentBehavior: PropTypes.oneOf(['always', 'automatic', 'never']),

    /**
     * Maximum size of area that can be displayed.
     *
     * @platform ios
     */
    maxDelta: PropTypes.number,

    /**
     * Minimum size of area that can be displayed.
     *
     * @platform ios
     */
    minDelta: PropTypes.number,

    /**
     * Callback that is called once the map is fully loaded.
     */
    onMapReady: PropTypes.func,

    /**
     * native only
     * Callback that is called continuously when the user is dragging the map.
     */
    onChange: PropTypes.func,
    /**
     * Callback that is called continuously when the user is dragging the map.
     */
    onRegionChange: PropTypes.func,

    /**
     * Callback that is called when the user is about to drag the map.
     */
    onRegionChangeStart: PropTypes.func,

    /**
     * Callback that is called when user taps on the map.
     */
    onPress: PropTypes.func,

    /**
     * Callback that is called when user makes a "long press" somewhere on the map.
     */
    onLongPress: PropTypes.func,

    /**
     * Callback that is called when the underlying map figures our users current location.
     */
    onUserLocationChange: PropTypes.func,

    /**
     * Callback that is called when a marker on the map is tapped by the user.
     */
    onMarkerPress: PropTypes.func,

    /**
     * Callback that is called when a marker on the map becomes selected. This will be called when
     * the callout for that marker is about to be shown.
     *
     * @platform ios
     */
    onMarkerSelect: PropTypes.func,

    /**
     * Callback that is called when a marker on the map becomes deselected. This will be called when
     * the callout for that marker is about to be hidden.
     *
     * @platform ios
     */
    onMarkerDeselect: PropTypes.func,

    /**
     * Callback that is called when a callout is tapped by the user.
     */
    onCalloutPress: PropTypes.func,

    /**
     * Callback that is called when the user initiates a drag on a marker (if it is draggable)
     */
    onMarkerDragStart: PropTypes.func,

    /**
     * Callback called continuously as a marker is dragged
     */
    onMarkerDrag: PropTypes.func,

    /**
     * Callback that is called when a drag on a marker finishes. This is usually the point you
     * will want to setState on the marker's coordinate again
     */
    onMarkerDragEnd: PropTypes.func,

    /**
     * Minimum zoom value for the map, must be between 0 and 20
     */
    minZoomLevel: PropTypes.number,

    /**
     * Maximum zoom value for the map, must be between 0 and 20
     */
    maxZoomLevel: PropTypes.number,

    onChangeStart: PropTypes.func,
};

class QMapView extends Component {
    constructor(props) {
        super(props);

        this.state = {
            isReady: Platform.OS === 'ios',
            ...props,
        };

        this._onMapReady = this._onMapReady.bind(this);
        this._onMarkerPress = this._onMarkerPress.bind(this);
        this._onChange = this._onChange.bind(this);
        this._onChangeStart = this._onChangeStart.bind(this);
        this._onLayout = this._onLayout.bind(this);
        this._onPress = this._onPress.bind(this);
        this._onLongPress = this._onLongPress.bind(this);
        this._onMarkerDrag = this._onMarkerDrag.bind(this);
        this._onMarkerDragStart = this._onMarkerDragStart.bind(this);
        this._onMarkerDragEnd = this._onMarkerDragEnd.bind(this);
        this._onUserLocationChange = this._onUserLocationChange.bind(this);
        this._onCalloutPress = this._onCalloutPress.bind(this);
    }

    setNativeProps(props) {
        this.setState(props);
    }

    static getDerivedStateFromProps(nextProps, prevState) {
        return nextProps;
    }

    componentDidUpdate() {
        const a = this.__lastRegion;
        const b = this.props.region;
        if (!a || !b) return;
        if (
            a.latitude !== b.latitude ||
            a.longitude !== b.longitude ||
            a.latitudeDelta !== b.latitudeDelta ||
            a.longitudeDelta !== b.longitudeDelta
        ) {
            this.setNativeProps({ region: b });
        }
    }

    _onMapReady() {
        const { region, initialRegion, onMapReady } = this.props;
        if (region) {
            this.setNativeProps({ region });
        } else if (initialRegion) {
            this.setNativeProps({ initialRegion });
        }

        this.setState({ isReady: true }, () => {
            if (onMapReady) onMapReady();
        });
    }

    _onLayout(e) {
        const { layout } = e.nativeEvent;
        if (!layout.width || !layout.height) return;
        if (this.state.isReady && !this.__layoutCalled) {
            const { region, initialRegion } = this.props;
            if (region) {
                this.__layoutCalled = true;
                this.setNativeProps({ region });
            } else if (initialRegion) {
                this.__layoutCalled = true;
                this.setNativeProps({ initialRegion });
            }
        }
        if (this.props.onLayout) {
            this.props.onLayout(e);
        }
    }

    _onMarkerPress(event) {
        if (this.props.onMarkerPress) {
            this.props.onMarkerPress(event.nativeEvent);
        }
    }

    _onPress(event) {
        if (this.props.onPress) {
            this.props.onPress(event.nativeEvent);
        }
    }

    _onLongPress(event) {
        if (this.props.onLongPress) {
            this.props.onLongPress(event.nativeEvent);
        }
    }

    _onMarkerDragStart(event) {
        if (this.props.onMarkerDragStart) {
            this.props.onMarkerDragStart(event.nativeEvent);
        }
    }

    _onMarkerDrag(event) {
        if (this.props.onMarkerDrag) {
            this.props.onMarkerDrag(event.nativeEvent);
        }
    }

    _onMarkerDragEnd(event) {
        if (this.props.onMarkerDragEnd) {
            this.props.onMarkerDragEnd(event.nativeEvent);
        }
    }

    _onUserLocationChange(event) {
        if (this.props.onUserLocationChange) {
            this.props.onUserLocationChange(event.nativeEvent);
        }
    }

    _onChangeStart(event) {
        if (this.props.onRegionChangeStart) {
            this.props.onRegionChangeStart(event.nativeEvent.region);
        }
    }

    _onChange(event) {
        if (this.props.onRegionChange) {
            this.props.onRegionChange(event.nativeEvent.region);
        }
    }

    animateToRegion(region, duration) {
        this._runCommand('animateToRegion', [region, duration || 500]);
    }

    animateToCoordinate(latLng, duration) {
        this._runCommand('animateToCoordinate', [latLng, duration || 500]);
    }

    fitToCoordinates(coordinates = [], options = {}) {
        const { edgePadding = { top: 0, right: 0, bottom: 0, left: 0 }, animated = true } = options;

        this._runCommand('fitToCoordinates', [coordinates, edgePadding, animated]);
    }

    drawRoute(startCoordinate, endCoordinate, options = {}) {
        let {
            transportType = 'walking',
            lineWidth = 2.0,
            lineColor = '#000000',
            needMapFit = true,
            edgePadding = { top: 0, right: 0, bottom: 0, left: 0 },
            animated = true,
        } = options;
        if (lineWidth <= 0) lineWidth = 2.0;
        if (lineColor == null || lineColor == undefined) lineColor = '#000000';
        if (transportType != 'walking' && transportType != 'automobile') transportType = 'walking';
        this._runCommand('drawRoute', [
            startCoordinate,
            endCoordinate,
            transportType,
            lineWidth,
            lineColor,
            needMapFit,
            edgePadding,
            animated,
        ]);
    }

    clearAllRoutes() {
        this._runCommand('clearAllRoutes', []);
    }

    setMapBoundaries(northEast, southWest) {
        this._runCommand('setMapBoundaries', [northEast, southWest]);
    }

    /**
     * Convert a map coordinate to user-space point
     *
     * @param coordinate Coordinate
     * @param [coordinate.latitude] Latitude
     * @param [coordinate.longitude] Longitude
     *
     * @return Promise Promise with the point ({ x: Number, y: Number })
     */
    pointForCoordinate(coordinate) {
        if (NativeModules.QMapModule) {
            return NativeModules.QMapModule.pointForCoordinate(this._getHandle(), coordinate);
        }

        return Promise.reject('pointForCoordinate not supported on this platform');
    }

    /**
     * Convert a user-space point to a map coordinate
     *
     * @param point Point
     * @param [point.x] X
     * @param [point.x] Y
     *
     * @return Promise Promise with the coordinate ({ latitude: Number, longitude: Number })
     */
    coordinateForPoint(point) {
        if (Platform.OS === 'android') {
            return NativeModules.AirMapModule.coordinateForPoint(this._getHandle(), point);
        } else if (Platform.OS === 'ios') {
            return this._runCommand('coordinateForPoint', [point]);
        }
        return Promise.reject('coordinateForPoint not supported on this platform');
    }

    // 执行百度地图 onPause()
    pauseMapView() {
        if (Platform.OS === 'android') {
            return NativeModules.QMapModule.pauseMapView(this._getHandle());
        }

        return Promise.reject('pauseMapView not supported on this platform');
    }

    // 执行百度地图 onResume()
    resumeMapView() {
        if (Platform.OS === 'android') {
            return NativeModules.QMapModule.resumeMapView(this._getHandle());
        }

        return Promise.reject('resumeMapView not supported on this platform');
    }

    _uiManagerCommand(name) {
        return NativeModules.UIManager.QMapView.Commands[name];
    }

    _mapManagerCommand(name) {
        return NativeModules[`QMapViewManager`][name];
    }

    _getHandle() {
        return findNodeHandle(this.map);
    }

    animateToRegion(region, duration) {
        this._runCommand('animateToRegion', [region, duration || 500]);
    }

    animateToCoordinate(latLng, duration) {
        this._runCommand('animateToCoordinate', [latLng, duration || 500]);
    }

    fitToSuppliedMarkers(markers, animated) {
        this._runCommand('fitToSuppliedMarkers', [markers, animated]);
    }

    fitToCoordinates(coordinates = [], options = {}) {
        const { edgePadding = { top: 0, right: 0, bottom: 0, left: 0 }, animated = true } = options;

        this._runCommand('fitToCoordinates', [coordinates, edgePadding, animated]);
    }

    _onCalloutPress(event) {
        if (this.props.stopPropagation) {
            event.stopPropagation();
        }

        if (this.props.onCalloutPress) {
            this.props.onCalloutPress(event.nativeEvent);
        }
    }

    getUIManagerCommand(name) {
        return NativeModules.UIManager.QMapView.Commands[name];
    }

    _runCommand(name, args) {
        return Commands[name](this.map, ...args);
    }

    render() {
        let props;

        if (this.state.isReady) {
            props = {
                region: null,
                initialRegion: null,
                coordinateType: 'GCJ02',
                ...this.state,
                onMarkerPress: this._onMarkerPress,
                onMapReady: this._onMapReady,
                onLayout: this._onLayout,
                onMarkerDrag: this._onMarkerDrag,
                onMarkerDragStart: this._onMarkerDragStart,
                onMarkerDragEnd: this._onMarkerDragEnd,
                onPress: this._onPress,
                onLongPress: this._onLongPress,
                onUserLocationChange: this._onUserLocationChange,
                onChange: this._onChange,
                onChangeStart: this._onChangeStart,
            };
        } else {
            props = {
                style: this.state.style,
                region: null,
                initialRegion: null,
                onMarkerPress: this._onMarkerPress,
                onChange: this._onChange,
                onMapReady: this._onMapReady,
                onLayout: this._onLayout,
                onChangeStart: this._onChangeStart,
            };
        }

        let MapView;
        const implType = this.state.implType;
        if (implType === 'baidu' && Platform.OS === 'ios') {
            MapView = requireNativeComponent('QBaiduMapView', QMapView);
        } else {
            MapView = requireNativeComponent('QMapView', QMapView);
        }

        return (
            <MapView
                ref={(ref) => {
                    this.map = ref;
                }}
                {...props}
            />
        );
    }
}

QMapView.propTypes = propTypes;
QMapView.Marker = MapMarker;

module.exports = QMapView;
