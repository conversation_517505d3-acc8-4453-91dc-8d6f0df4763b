/**
 *
 * @providesModule QCacheRiskControl
 */
'use strict';

const QRCTCacheRiskControl = require('react-native').NativeModules.QRCTCacheRiskControl;

const QCacheRiskControl = {
    cacheRiskControl: function (backend, callback = () => {}) {
        if (QRCTCacheRiskControl && QRCTCacheRiskControl.cacheRiskControl) {
            QRCTCacheRiskControl.cacheRiskControl(backend, callback);
        } else {
            callback({
                error: '暂不支持'
            });
        }
    }
};

module.exports = QCacheRiskControl;
