'use strict'
const schemeProtocol = require('qunar-react-native').DeviceInfo.scheme;

class QMiniProgram {
    launchMiniProgram(param, callback) {
        if(!param ||!param.userName){
          console.error('userName 参数必传');
          return;
        }
        let scheme = schemeProtocol +'://miniprogram/launchMiniProgram';
        let params = [];
        Object.keys(param).forEach((v) => {
          let paramStr = param[v];
          if(v === 'path'){
            paramStr = encodeURIComponent(paramStr);
          }
          params.push(`${v}=${(paramStr)}`);
        });
        scheme = `${scheme}?${params.join('&')}`;
        Ext.Router.Bridge.sendScheme({
          url: scheme
        }, callback);
      }
}

module.exports = new QMiniProgram();
