/**
 *
 * @providesModule QExpose
 */
'use strict';

const QRCTExpose = require('react-native').NativeModules.QRCTExpose;

const QExpose = {
    takeOverExpose: function (param, callback = () => {}, failCallback = () => {}) {
        // 判断param是否为对象
        if (typeof param !== 'object') {
            failCallback({
                error: 'param 必须为 Object',
            });

            return;
        }

        if (QRCTExpose && QRCTExpose.takeOverExpose) {
            QRCTExpose.takeOverExpose(param, callback, failCallback);
        } else {
            failCallback({
                error: '暂不支持',
            });
        }
    },
    startExposeIfRenderEnd: function (param, callback = () => {}, failCallback = () => {}) {
        // 判断param是否为对象
        if (typeof param !== 'object') {
            failCallback({
                error: 'param 必须为 Object',
            });

            return;
        }
        if (QRCTExpose && QRCTExpose.startExposeIfRenderEnd) {
            QRCTExpose.startExposeIfRenderEnd(param, callback, failCallback);
        } else {
            failCallback({
                error: '暂不支持',
            });
        }
    },
};

module.exports = QExpose;
