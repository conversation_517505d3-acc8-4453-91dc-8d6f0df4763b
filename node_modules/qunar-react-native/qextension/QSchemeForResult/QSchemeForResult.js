/**
 *
 * @providesModule QSchemeForResult
 */
"use strict";

const { NativeModules } = require("react-native");

const QSchemeForResult = {
  schemeForResult(url, readableMap = null, adrToken = "", callback = () => {}) {
    if (!url) {
      throw new Error("url参数必传");
      return;
    }
    NativeModules.QRCTJumpHandleManager &&
      NativeModules.QRCTJumpHandleManager.sendScheme &&
      NativeModules.QRCTJumpHandleManager.sendScheme(
        url,
        readableMap,
        adrToken,
        callback
      );
  },
};

module.exports = QSchemeForResult;
