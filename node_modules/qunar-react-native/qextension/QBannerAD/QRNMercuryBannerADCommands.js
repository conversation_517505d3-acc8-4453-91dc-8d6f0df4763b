/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 * @flow strict-local
 */

import codegenNativeCommands from 'react-native/Libraries/Utilities/codegenNativeCommands';

interface NativeCommands {
    +destroyBanner: (viewRef: React.ElementRef<ComponentType>) => void;
}

export default (codegenNativeCommands<NativeCommands>({
    supportedCommands: [
        'destroyBanner',
    ],
}): NativeCommands);