/**
 *
 * @providesModule QRNMercuryBannerAD
 * @flow
 */
import React, { Component } from 'react';
import {requireNativeComponent,ViewPropTypes} from 'react-native';
import PropTypes from 'prop-types';
import Commands from './QRNMercuryBannerADCommands';


const propTypes = {
    ...ViewPropTypes,
    style: ViewPropTypes.style,
    initProps: PropTypes.object,
    onADReceived: PropTypes.func,
    onADFailedToReceive: PropTypes.func,
    onADClosed: PropTypes.func,
    onADLeftApplication: PropTypes.func,
    onADExposure: PropTypes.func,
    onADClicked: PropTypes.func,

}

class QRNMercuryBannerAD extends Component {
    constructor(props) {
        super(props);
    }

  
    _onADReceived = (event) => {
        if (this.props.onADReceived) {
            this.props.onADReceived(event.nativeEvent);
        }
    };
    _onADClosed = (event) => {
        if (this.props.onADClosed) {
            this.props.onADClosed(event.nativeEvent);
        }
    }; _onADLeftApplication = (event) => {
        if (this.props.onADLeftApplication) {
            this.props.onADLeftApplication(event.nativeEvent);
        }
    }; _onADExposure = (event) => {
        if (this.props.onADExposure) {
            this.props.onADExposure(event.nativeEvent);
        }
    }; _onADClicked = (event) => {
        if (this.props.onADClicked) {
            this.props.onADClicked(event.nativeEvent);
        }
    };
    _onADFailedToReceive = (event) => {
        if (this.props.onADFailedToReceive) {
            this.props.onADFailedToReceive(event.nativeEvent);
        }
    };
    destroyBanner() {
        this._runCommand('destroyBanner', []);
    }
 
    _runCommand(name, args) {
        return Commands[name](this.bannerADView, ...args);
    }
  

    render() {
        // console.log('xxxxx'+JSON.stringify(this.props))

        const addtionProps = {
            ...this.props,
            onADReceived: this._onADReceived,
            onADClosed: this._onADClosed,
            onADLeftApplication: this._onADLeftApplication,
            onADExposure: this._onADExposure,
            onADClicked: this._onADClicked,
            onADFailedToReceive: this._onADFailedToReceive,
        };

        return (

            <QRNMercuryBannerADView
                ref={(ref) => {
                    this.bannerADView = ref;
                }}
                {...addtionProps}

            />
        );
    }
}

QRNMercuryBannerAD.propTypes = propTypes;
QRNMercuryBannerAD.displayName = 'QRNMercuryBannerAD';

const QRNMercuryBannerADView = requireNativeComponent('QRNMercuryBannerADView', QRNMercuryBannerAD);

module.exports = QRNMercuryBannerAD;