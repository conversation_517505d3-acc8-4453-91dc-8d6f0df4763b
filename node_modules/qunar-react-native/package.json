{"name": "qunar-react-native", "version": "7.1.8", "description": "qrn --> qrn adapter", "main": "index.js", "dependencies": {"@qnpm/babel-plugin-qrn": "2.0.6", "@qnpm/react-native-ext": "git+ssh://*************************:qrn/qrn-ext.git#RN77", "@react-native-camera-roll/camera-roll": "5.1.0", "@react-native-community/geolocation": "2.0.2", "@react-native-community/netinfo": "4.4.0", "@react-native-community/viewpager": "3.1.0", "react-native-pager-view": "6.2.3", "deprecated-react-native-listview": "0.0.8", "@react-native-community/art": "1.2.0", "@react-native-community/async-storage": "1.12.0", "@react-native-async-storage/async-storage": "1.21.0", "@react-native-community/clipboard": "1.3.0", "@react-native-clipboard/clipboard": "1.13.2", "@react-native-community/checkbox": "0.5.20", "@react-native-community/datetimepicker": "3.0.2", "@react-native-community/masked-view": "0.1.10", "@react-native-masked-view/masked-view": "0.2.9", "@react-native-community/picker": "1.8.0", "@react-native-picker/picker": "2.11.1", "@react-native-community/progress-bar-android": "1.0.3", "@react-native-community/progress-view": "1.4.2", "@react-native-community/segmented-control": "2.1.2", "@react-native-community/slider": "3.0.3", "deprecated-react-native-swipeable-listview": "0.0.1", "react-native-svg": "15.11.2", "react-native": "0.77.1", "deprecated-react-native-prop-types": "2.3.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native-webview": "13.14.2", "lodash": "4.17.15", "whatwg-fetch": "3.0.0", "uuid": "^3.4.0", "deepmerge": "4.3.1", "react-is": "16.9.0", "hoist-non-react-statics": "3.3.2", "tracer": "1.1.6"}, "overrides": {"react-native": "0.77.1", "@react-native-camera-roll/camera-roll": "5.1.0", "deprecated-react-native-prop-types": "2.3.0", "metro": "0.81.5", "metro-babel-register": "0.81.5", "metro-babel-transformer": "0.81.5", "metro-cache": "0.81.5", "metro-config": "0.81.5", "metro-core": "0.81.5", "metro-minify-terser": "0.81.5", "metro-react-native-babel-preset": "0.77.0", "metro-react-native-babel-transformer": "0.77.0", "metro-resolver": "0.81.5", "metro-runtime": "0.81.5", "metro-source-map": "0.81.5", "metro-symbolicate": "0.81.5", "@react-native-community/cli": "15.1.3", "@react-native-community/cli-config": "15.1.3", "@react-native-community/cli-debugger-ui": "15.1.3", "@react-native-community/cli-platform-android": "15.1.3", "@react-native-community/cli-platform-ios": "15.1.3", "@react-native-community/cli-tools": "15.1.3", "@react-native-community/cli-types": "15.1.3", "@react-native/metro-config": "0.77.1", "@react-native/babel-preset": "0.77.1", "@react-native/virtualized-lists": "0.77.1", "chalk": "4.1.2", "babel-plugin-transform-remove-console": "6.9.4", "react": "18.3.1", "react-dom": "18.3.1", "@babel/code-frame": "7.27.1", "@babel/compat-data": "7.28.0", "@babel/core": "7.28.0", "@babel/generator": "7.28.0", "@babel/helper-annotate-as-pure": "7.27.3", "@babel/helper-compilation-targets": "7.27.2", "@babel/helper-create-class-features-plugin": "7.27.1", "@babel/helper-create-regexp-features-plugin": "7.27.1", "@babel/helper-define-polyfill-provider": "0.6.5", "@babel/helper-environment-visitor": "7.24.7", "@babel/helper-globals": "7.28.0", "@babel/helper-member-expression-to-functions": "7.27.1", "@babel/helper-module-imports": "7.27.1", "@babel/helper-module-transforms": "7.27.3", "@babel/helper-optimise-call-expression": "7.27.1", "@babel/helper-plugin-utils": "7.27.1", "@babel/helper-remap-async-to-generator": "7.27.1", "@babel/helper-replace-supers": "7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "7.27.1", "@babel/helper-string-parser": "7.27.1", "@babel/helper-validator-identifier": "7.27.1", "@babel/helper-validator-option": "7.27.1", "@babel/helper-wrap-function": "7.27.1", "@babel/helpers": "7.27.6", "@babel/parser": "7.28.0", "@babel/plugin-proposal-async-generator-functions": "7.20.7", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-export-default-from": "7.27.1", "@babel/plugin-proposal-export-namespace-from": "7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-numeric-separator": "7.18.6", "@babel/plugin-proposal-object-rest-spread": "7.20.7", "@babel/plugin-proposal-optional-catch-binding": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-syntax-async-generators": "7.8.4", "@babel/plugin-syntax-bigint": "7.8.3", "@babel/plugin-syntax-class-properties": "7.12.13", "@babel/plugin-syntax-class-static-block": "7.14.5", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-syntax-export-default-from": "7.27.1", "@babel/plugin-syntax-export-namespace-from": "7.8.3", "@babel/plugin-syntax-flow": "7.27.1", "@babel/plugin-syntax-import-assertions": "7.27.1", "@babel/plugin-syntax-import-attributes": "7.27.1", "@babel/plugin-syntax-import-meta": "7.10.4", "@babel/plugin-syntax-json-strings": "7.8.3", "@babel/plugin-syntax-jsx": "7.27.1", "@babel/plugin-syntax-logical-assignment-operators": "7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "7.8.3", "@babel/plugin-syntax-numeric-separator": "7.10.4", "@babel/plugin-syntax-object-rest-spread": "7.8.3", "@babel/plugin-syntax-optional-catch-binding": "7.8.3", "@babel/plugin-syntax-optional-chaining": "7.8.3", "@babel/plugin-syntax-private-property-in-object": "7.14.5", "@babel/plugin-syntax-top-level-await": "7.14.5", "@babel/plugin-syntax-typescript": "7.27.1", "@babel/plugin-syntax-unicode-sets-regex": "7.18.6", "@babel/plugin-transform-arrow-functions": "7.27.1", "@babel/plugin-transform-async-generator-functions": "7.28.0", "@babel/plugin-transform-async-to-generator": "7.27.1", "@babel/plugin-transform-block-scoped-functions": "7.27.1", "@babel/plugin-transform-block-scoping": "7.28.0", "@babel/plugin-transform-class-properties": "7.27.1", "@babel/plugin-transform-class-static-block": "7.27.1", "@babel/plugin-transform-classes": "7.28.0", "@babel/plugin-transform-computed-properties": "7.27.1", "@babel/plugin-transform-destructuring": "7.28.0", "@babel/plugin-transform-dotall-regex": "7.27.1", "@babel/plugin-transform-duplicate-keys": "7.27.1", "@babel/plugin-transform-duplicate-named-capturing-groups-regex": "7.27.1", "@babel/plugin-transform-dynamic-import": "7.27.1", "@babel/plugin-transform-explicit-resource-management": "7.28.0", "@babel/plugin-transform-exponentiation-operator": "7.27.1", "@babel/plugin-transform-export-namespace-from": "7.27.1", "@babel/plugin-transform-flow-strip-types": "7.27.1", "@babel/plugin-transform-for-of": "7.27.1", "@babel/plugin-transform-function-name": "7.27.1", "@babel/plugin-transform-json-strings": "7.27.1", "@babel/plugin-transform-literals": "7.27.1", "@babel/plugin-transform-logical-assignment-operators": "7.27.1", "@babel/plugin-transform-member-expression-literals": "7.27.1", "@babel/plugin-transform-modules-amd": "7.27.1", "@babel/plugin-transform-modules-commonjs": "7.27.1", "@babel/plugin-transform-modules-systemjs": "7.27.1", "@babel/plugin-transform-modules-umd": "7.27.1", "@babel/plugin-transform-named-capturing-groups-regex": "7.27.1", "@babel/plugin-transform-new-target": "7.27.1", "@babel/plugin-transform-nullish-coalescing-operator": "7.27.1", "@babel/plugin-transform-numeric-separator": "7.27.1", "@babel/plugin-transform-object-rest-spread": "7.28.0", "@babel/plugin-transform-object-super": "7.27.1", "@babel/plugin-transform-optional-catch-binding": "7.27.1", "@babel/plugin-transform-optional-chaining": "7.27.1", "@babel/plugin-transform-parameters": "7.27.7", "@babel/plugin-transform-private-methods": "7.27.1", "@babel/plugin-transform-private-property-in-object": "7.27.1", "@babel/plugin-transform-property-literals": "7.27.1", "@babel/plugin-transform-react-display-name": "7.28.0", "@babel/plugin-transform-react-jsx": "7.27.1", "@babel/plugin-transform-react-jsx-self": "7.27.1", "@babel/plugin-transform-react-jsx-source": "7.27.1", "@babel/plugin-transform-regenerator": "7.28.0", "@babel/plugin-transform-regexp-modifiers": "7.27.1", "@babel/plugin-transform-reserved-words": "7.27.1", "@babel/plugin-transform-runtime": "7.28.0", "@babel/plugin-transform-shorthand-properties": "7.27.1", "@babel/plugin-transform-spread": "7.27.1", "@babel/plugin-transform-sticky-regex": "7.27.1", "@babel/plugin-transform-template-literals": "7.27.1", "@babel/plugin-transform-typeof-symbol": "7.27.1", "@babel/plugin-transform-typescript": "7.28.0", "@babel/plugin-transform-unicode-escapes": "7.27.1", "@babel/plugin-transform-unicode-property-regex": "7.27.1", "@babel/plugin-transform-unicode-regex": "7.27.1", "@babel/plugin-transform-unicode-sets-regex": "7.27.1", "@babel/preset-env": "7.28.0", "@babel/preset-flow": "7.27.1", "@babel/preset-modules": "0.1.6-no-external-plugins", "@babel/preset-typescript": "7.27.1", "@babel/register": "7.27.1", "@babel/runtime": "7.27.6", "@babel/template": "7.27.2", "@babel/traverse": "7.28.0", "@babel/types": "7.28.0"}, "dependencies-rn72": {"react-native-harmony": "git+ssh://*************************:qrn/react-native-harmony.git#RN72", "@qnpm/babel-plugin-qrn": "2.0.6", "@qnpm/react-native-ext": "git+ssh://*************************:qrn/qrn-ext.git#RN72", "@react-native-community/cameraroll": "1.2.1", "@react-native-community/geolocation": "2.0.2", "@react-native-community/netinfo": "http://gitlab.corp.qunar.com/qrn/react-native-oh-tpl-3rd/raw/release/react-native-oh-tpl-netinfo-11.1.0-0.0.7.tgz", "@react-native-community/viewpager": "3.1.0", "deprecated-react-native-listview": "0.0.8", "@react-native-community/art": "1.2.0", "@react-native-async-storage/async-storage": "http://gitlab.corp.qunar.com/qrn/react-native-oh-tpl-3rd/raw/release/react-native-oh-tpl-async-storage-1.21.0-0.1.6.tgz", "@react-native-clipboard/clipboard": "http://gitlab.corp.qunar.com/qrn/react-native-oh-tpl-3rd/raw/release/react-native-oh-tpl-clipboard-1.13.2-0.0.6.tgz", "@react-native-community/checkbox": "http://gitlab.corp.qunar.com/qrn/react-native-oh-tpl-3rd/raw/release/react-native-oh-tpl-checkbox-0.5.16-0.0.7.tgz", "react-native-pager-view": "git+ssh://*************************:qrn/react-native-pager-view.git#v6.2.3-0.2.8", "@react-native-community/progress-view": "http://gitlab.corp.qunar.com/qrn/react-native-oh-tpl-3rd/raw/release/react-native-oh-library-progress-view-1.4.2-0.0.6.tgz", "@react-native-community/datetimepicker": "3.0.2", "@react-native-masked-view/masked-view": "http://gitlab.corp.qunar.com/qrn/react-native-oh-tpl-3rd/raw/release/react-native-oh-tpl-masked-view-0.2.9-0.2.3-rc.1.tgz", "@react-native-community/masked-view": "0.1.10", "@react-native-picker/picker": "http://gitlab.corp.qunar.com/qrn/react-native-oh-tpl-3rd/raw/release/react-native-oh-tpl-picker-2.6.1-0.2.4.tgz", "@react-native-community/progress-bar-android": "http://gitlab.corp.qunar.com/qrn/react-native-oh-tpl-3rd/raw/release/react-native-oh-tpl-progress-bar-android-1.0.4-0.1.1.tgz", "react-native-svg": "15.0.0", "@react-native-oh-tpl/react-native-svg": "15.0.0-0.5.8", "@react-native-community/segmented-control": "2.1.2", "@react-native-community/slider": "3.0.3", "@react-native-community/image-picker-ios": "1.0.1", "deprecated-react-native-swipeable-listview": "0.0.1", "@react-native-oh-tpl/react-native-video": "5.2.1-0.2.30", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.72.5", "@react-native/metro-config": "0.72.11", "react-native-webview": "http://gitlab.corp.qunar.com/qrn/react-native-oh-tpl-3rd/raw/release/react-native-oh-tpl-react-native-webview-13.10.2-0.2.33.tgz", "whatwg-fetch": "3.0.0", "react-native-codegen": "0.0.13", "@babel/runtime": "7.17.9", "@babel/core": "7.17.9", "@babel/types": "7.17.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.16.7", "@babel/plugin-proposal-optional-chaining": "7.16.7", "@babel/plugin-syntax-class-properties": "7.12.13", "@babel/plugin-transform-flow-strip-types": "7.16.7", "@babel/plugin-transform-modules-commonjs": "7.17.9", "@babel/preset-modules": "0.1.4", "@babel/code-frame": "7.5.5", "@babel/compat-data": "7.18.8", "@babel/generator": "7.18.9", "@babel/helper-annotate-as-pure": "7.18.6", "@babel/helper-builder-binary-assignment-operator-visitor": "7.18.9", "@babel/helper-builder-react-jsx": "7.7.4", "@babel/helper-compilation-targets": "7.18.9", "@babel/helper-create-class-features-plugin": "7.18.9", "@babel/helper-create-regexp-features-plugin": "7.18.6", "@babel/helper-define-polyfill-provider": "0.3.2", "@babel/helper-environment-visitor": "7.18.9", "@babel/helper-explode-assignable-expression": "7.18.6", "@babel/helper-function-name": "7.18.9", "@babel/helper-hoist-variables": "7.18.6", "@babel/helper-member-expression-to-functions": "7.18.9", "@babel/helper-module-imports": "7.18.6", "@babel/helper-module-transforms": "7.18.9", "@babel/helper-optimise-call-expression": "7.18.6", "@babel/helper-plugin-utils": "7.0.0", "@babel/helper-remap-async-to-generator": "7.18.9", "@babel/helper-replace-supers": "7.18.9", "@babel/helper-simple-access": "7.18.6", "@babel/helper-skip-transparent-expression-wrappers": "7.18.9", "@babel/helper-split-export-declaration": "7.18.6", "@babel/helper-validator-identifier": "7.18.6", "@babel/helper-validator-option": "7.18.6", "@babel/helper-wrap-function": "7.18.9", "@babel/helpers": "7.18.9", "@babel/highlight": "7.5.0", "@babel/parser": "7.18.9", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-export-default-from": "7.18.9", "@babel/plugin-proposal-object-rest-spread": "7.18.9", "@babel/plugin-proposal-optional-catch-binding": "7.18.6", "@babel/plugin-proposal-unicode-property-regex": "7.18.6", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-syntax-export-default-from": "7.18.6", "@babel/plugin-syntax-flow": "7.18.6", "@babel/plugin-syntax-jsx": "7.7.4", "@babel/plugin-syntax-nullish-coalescing-operator": "7.8.3", "@babel/plugin-syntax-object-rest-spread": "7.8.3", "@babel/plugin-syntax-optional-catch-binding": "7.8.3", "@babel/plugin-syntax-optional-chaining": "7.8.3", "@babel/plugin-syntax-typescript": "7.18.6", "@babel/plugin-transform-arrow-functions": "7.18.6", "@babel/plugin-transform-async-to-generator": "7.16.8", "@babel/plugin-transform-block-scoped-functions": "7.18.6", "@babel/plugin-transform-block-scoping": "7.18.9", "@babel/plugin-transform-classes": "7.18.9", "@babel/plugin-transform-computed-properties": "7.18.9", "@babel/plugin-transform-destructuring": "7.18.9", "@babel/plugin-transform-dotall-regex": "7.18.6", "@babel/plugin-transform-exponentiation-operator": "7.18.6", "@babel/plugin-transform-for-of": "7.18.8", "@babel/plugin-transform-function-name": "7.18.9", "@babel/plugin-transform-literals": "7.18.9", "@babel/plugin-transform-member-expression-literals": "7.18.6", "@babel/plugin-transform-object-assign": "7.18.6", "@babel/plugin-transform-object-super": "7.18.6", "@babel/plugin-transform-parameters": "7.18.8", "@babel/plugin-transform-property-literals": "7.18.6", "@babel/plugin-transform-react-display-name": "7.18.6", "@babel/plugin-transform-react-jsx": "7.18.6", "@babel/plugin-transform-react-jsx-self": "7.18.6", "@babel/plugin-transform-react-jsx-source": "7.18.6", "@babel/plugin-transform-regenerator": "7.18.6", "@babel/plugin-transform-runtime": "7.18.9", "@babel/plugin-transform-shorthand-properties": "7.18.6", "@babel/plugin-transform-spread": "7.18.9", "@babel/plugin-transform-sticky-regex": "7.18.6", "@babel/plugin-transform-template-literals": "7.18.9", "@babel/plugin-transform-typescript": "7.18.8", "@babel/plugin-transform-unicode-regex": "7.18.6", "@babel/plugin-syntax-decorators": "7.18.6", "@babel/plugin-proposal-decorators": "7.18.6", "@babel/preset-flow": "7.18.6", "@babel/preset-typescript": "7.18.6", "@babel/register": "7.18.9", "@babel/template": "7.18.6", "@babel/traverse": "7.18.9", "babel-plugin-transform-remove-console": "6.9.4", "esutils": "2.0.0", "lodash": "4.17.15", "metro": "0.76.8", "metro-cache": "0.76.8", "metro-config": "0.76.8", "metro-core": "0.76.8", "metro-babel-register": "0.76.8", "metro-babel-transformer": "0.76.8", "metro-react-native-babel-preset": "0.76.8", "metro-symbolicate": "0.76.8", "metro-minify-uglify": "0.76.8", "metro-resolver": "0.76.8", "metro-react-native-babel-transformer": "0.76.8", "metro-runtime": "0.76.8", "metro-source-map": "0.76.8", "metro-minify-terser": "0.76.8", "uuid": "^3.4.0", "deepmerge": "4.3.1"}, "overrides-rn72": {"@react-native-community/cameraroll": "1.2.1", "@react-native-community/geolocation": "2.0.2", "@react-native-community/viewpager": "3.1.0", "deprecated-react-native-listview": "0.0.8", "@react-native-community/art": "1.2.0", "react-native-svg": "15.0.0", "@react-native-community/datetimepicker": "3.0.2", "@react-native-community/masked-view": "0.1.10", "@react-native-community/segmented-control": "2.1.2", "@react-native-community/slider": "3.0.3", "@react-native-community/image-picker-ios": "1.0.1", "deprecated-react-native-swipeable-listview": "0.0.1", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.72.5", "@react-native/metro-config": "0.72.11", "whatwg-fetch": "3.0.0", "react-native-codegen": "0.0.13", "@babel/runtime": "7.17.9", "@babel/core": "7.17.9", "@babel/types": "7.17.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.16.7", "babel-plugin-transform-remove-console": "6.9.4", "@babel/plugin-proposal-optional-chaining": "7.16.7", "@babel/plugin-syntax-class-properties": "7.12.13", "@babel/plugin-transform-flow-strip-types": "7.16.7", "@babel/plugin-transform-modules-commonjs": "7.17.9", "@babel/preset-modules": "0.1.4", "@babel/code-frame": "7.5.5", "@babel/compat-data": "7.18.8", "@babel/generator": "7.18.9", "@babel/helper-annotate-as-pure": "7.18.6", "@babel/helper-builder-binary-assignment-operator-visitor": "7.18.9", "@babel/helper-builder-react-jsx": "7.7.4", "@babel/helper-compilation-targets": "7.18.9", "@babel/helper-create-class-features-plugin": "7.18.9", "@babel/helper-create-regexp-features-plugin": "7.18.6", "@babel/helper-define-polyfill-provider": "0.3.2", "@babel/helper-environment-visitor": "7.18.9", "@babel/helper-explode-assignable-expression": "7.18.6", "@babel/helper-function-name": "7.18.9", "@babel/helper-hoist-variables": "7.18.6", "@babel/helper-member-expression-to-functions": "7.18.9", "@babel/helper-module-imports": "7.18.6", "@babel/helper-module-transforms": "7.18.9", "@babel/helper-optimise-call-expression": "7.18.6", "@babel/helper-remap-async-to-generator": "7.18.9", "@babel/helper-replace-supers": "7.18.9", "@babel/helper-simple-access": "7.18.6", "@babel/helper-skip-transparent-expression-wrappers": "7.18.9", "@babel/helper-split-export-declaration": "7.18.6", "@babel/helper-validator-identifier": "7.18.6", "@babel/helper-validator-option": "7.18.6", "@babel/helper-wrap-function": "7.18.9", "@babel/helpers": "7.18.9", "@babel/highlight": "7.5.0", "@babel/parser": "7.18.9", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-export-default-from": "7.18.9", "@babel/plugin-proposal-object-rest-spread": "7.18.9", "@babel/plugin-proposal-optional-catch-binding": "7.18.6", "@babel/plugin-proposal-unicode-property-regex": "7.18.6", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-syntax-export-default-from": "7.18.6", "@babel/plugin-syntax-flow": "7.18.6", "@babel/plugin-syntax-jsx": "7.7.4", "@babel/plugin-syntax-nullish-coalescing-operator": "7.8.3", "@babel/plugin-syntax-object-rest-spread": "7.8.3", "@babel/plugin-syntax-optional-catch-binding": "7.8.3", "@babel/plugin-syntax-optional-chaining": "7.8.3", "@babel/plugin-syntax-typescript": "7.18.6", "@babel/plugin-transform-arrow-functions": "7.18.6", "@babel/plugin-transform-async-to-generator": "7.16.8", "@babel/plugin-transform-block-scoped-functions": "7.18.6", "@babel/plugin-transform-block-scoping": "7.18.9", "@babel/plugin-transform-classes": "7.18.9", "@babel/plugin-transform-computed-properties": "7.18.9", "@babel/plugin-transform-destructuring": "7.18.9", "@babel/plugin-transform-dotall-regex": "7.18.6", "@babel/plugin-transform-exponentiation-operator": "7.18.6", "@babel/plugin-transform-for-of": "7.18.8", "@babel/plugin-transform-function-name": "7.18.9", "@babel/plugin-transform-literals": "7.18.9", "@babel/plugin-transform-member-expression-literals": "7.18.6", "@babel/plugin-transform-object-assign": "7.18.6", "@babel/plugin-transform-object-super": "7.18.6", "@babel/plugin-transform-parameters": "7.18.8", "@babel/plugin-transform-property-literals": "7.18.6", "@babel/plugin-transform-react-display-name": "7.18.6", "@babel/plugin-transform-react-jsx": "7.18.6", "@babel/plugin-transform-react-jsx-self": "7.18.6", "@babel/plugin-transform-react-jsx-source": "7.18.6", "@babel/plugin-transform-regenerator": "7.18.6", "@babel/plugin-transform-runtime": "7.18.9", "@babel/plugin-transform-shorthand-properties": "7.18.6", "@babel/plugin-transform-spread": "7.18.9", "@babel/plugin-transform-sticky-regex": "7.18.6", "@babel/plugin-transform-template-literals": "7.18.9", "@babel/plugin-transform-typescript": "7.18.8", "@babel/plugin-transform-unicode-regex": "7.18.6", "@babel/plugin-syntax-decorators": "7.18.6", "@babel/plugin-proposal-decorators": "7.18.6", "@babel/preset-flow": "7.18.6", "@babel/preset-typescript": "7.18.6", "@babel/register": "7.18.9", "@babel/template": "7.18.6", "@babel/traverse": "7.18.9", "esutils": "2.0.0", "lodash": "4.17.15", "metro": "0.76.8", "metro-cache": "0.76.8", "metro-config": "0.76.8", "metro-core": "0.76.8", "metro-babel-register": "0.76.8", "metro-babel-transformer": "0.76.8", "metro-react-native-babel-preset": "0.76.8", "metro-symbolicate": "0.76.8", "metro-minify-uglify": "0.76.8", "metro-resolver": "0.76.8", "metro-react-native-babel-transformer": "0.76.8", "metro-runtime": "0.76.8", "metro-source-map": "0.76.8", "metro-minify-terser": "0.76.8", "@react-native-community/cli": "11.3.7", "@react-native-community/cli-debugger-ui": "11.3.7", "@react-native-community/cli-platform-android": "11.3.7", "@react-native-community/cli-platform-ios": "11.3.7", "@react-native-community/cli-tools": "11.3.7", "@react-native-community/cli-types": "11.3.7", "@react-native-community/cli-config": "11.3.7", "@react-native-community/cli-plugin-metro": "11.3.7", "@react-native-community/cli-hermes": "11.3.7", "@react-native/virtualized-lists": "0.72.8", "hermes-engine": "0.11.0"}, "dependencies-rn77": {"@qnpm/babel-plugin-qrn": "2.0.6", "@qnpm/react-native-ext": "git+ssh://*************************:qrn/qrn-ext.git#RN77", "@react-native-camera-roll/camera-roll": "5.1.0", "@react-native-community/geolocation": "2.0.2", "@react-native-community/netinfo": "4.4.0", "@react-native-community/viewpager": "3.1.0", "react-native-pager-view": "6.2.3", "deprecated-react-native-listview": "0.0.8", "@react-native-community/art": "1.2.0", "@react-native-community/async-storage": "1.12.0", "@react-native-async-storage/async-storage": "1.21.0", "@react-native-community/clipboard": "1.3.0", "@react-native-clipboard/clipboard": "1.13.2", "@react-native-community/checkbox": "0.5.20", "@react-native-community/datetimepicker": "3.0.2", "@react-native-community/masked-view": "0.1.10", "@react-native-masked-view/masked-view": "0.2.9", "@react-native-community/picker": "1.8.0", "@react-native-picker/picker": "2.11.1", "@react-native-community/progress-bar-android": "1.0.3", "@react-native-community/progress-view": "1.4.2", "@react-native-community/segmented-control": "2.1.2", "@react-native-community/slider": "3.0.3", "deprecated-react-native-swipeable-listview": "0.0.1", "react-native-svg": "15.11.2", "react-native": "0.77.1", "deprecated-react-native-prop-types": "2.3.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native-webview": "13.14.2", "lodash": "4.17.15", "whatwg-fetch": "3.0.0", "uuid": "^3.4.0", "deepmerge": "4.3.1", "react-is": "16.9.0", "hoist-non-react-statics": "3.3.2", "tracer": "1.1.6"}, "overrides-rn77": {"react-native": "0.77.1", "@react-native-camera-roll/camera-roll": "5.1.0", "deprecated-react-native-prop-types": "2.3.0", "metro": "0.81.5", "metro-babel-register": "0.81.5", "metro-babel-transformer": "0.81.5", "metro-cache": "0.81.5", "metro-config": "0.81.5", "metro-core": "0.81.5", "metro-minify-terser": "0.81.5", "metro-react-native-babel-preset": "0.77.0", "metro-react-native-babel-transformer": "0.77.0", "metro-resolver": "0.81.5", "metro-runtime": "0.81.5", "metro-source-map": "0.81.5", "metro-symbolicate": "0.81.5", "@react-native-community/cli": "15.1.3", "@react-native-community/cli-config": "15.1.3", "@react-native-community/cli-debugger-ui": "15.1.3", "@react-native-community/cli-platform-android": "15.1.3", "@react-native-community/cli-platform-ios": "15.1.3", "@react-native-community/cli-tools": "15.1.3", "@react-native-community/cli-types": "15.1.3", "@react-native/metro-config": "0.77.1", "@react-native/babel-preset": "0.77.1", "@react-native/virtualized-lists": "0.77.1", "chalk": "4.1.2", "babel-plugin-transform-remove-console": "6.9.4", "react": "18.3.1", "react-dom": "18.3.1", "@babel/code-frame": "7.27.1", "@babel/compat-data": "7.28.0", "@babel/core": "7.28.0", "@babel/generator": "7.28.0", "@babel/helper-annotate-as-pure": "7.27.3", "@babel/helper-compilation-targets": "7.27.2", "@babel/helper-create-class-features-plugin": "7.27.1", "@babel/helper-create-regexp-features-plugin": "7.27.1", "@babel/helper-define-polyfill-provider": "0.6.5", "@babel/helper-environment-visitor": "7.24.7", "@babel/helper-globals": "7.28.0", "@babel/helper-member-expression-to-functions": "7.27.1", "@babel/helper-module-imports": "7.27.1", "@babel/helper-module-transforms": "7.27.3", "@babel/helper-optimise-call-expression": "7.27.1", "@babel/helper-plugin-utils": "7.27.1", "@babel/helper-remap-async-to-generator": "7.27.1", "@babel/helper-replace-supers": "7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "7.27.1", "@babel/helper-string-parser": "7.27.1", "@babel/helper-validator-identifier": "7.27.1", "@babel/helper-validator-option": "7.27.1", "@babel/helper-wrap-function": "7.27.1", "@babel/helpers": "7.27.6", "@babel/parser": "7.28.0", "@babel/plugin-proposal-async-generator-functions": "7.20.7", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-export-default-from": "7.27.1", "@babel/plugin-proposal-export-namespace-from": "7.18.9", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-numeric-separator": "7.18.6", "@babel/plugin-proposal-object-rest-spread": "7.20.7", "@babel/plugin-proposal-optional-catch-binding": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-syntax-async-generators": "7.8.4", "@babel/plugin-syntax-bigint": "7.8.3", "@babel/plugin-syntax-class-properties": "7.12.13", "@babel/plugin-syntax-class-static-block": "7.14.5", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-syntax-export-default-from": "7.27.1", "@babel/plugin-syntax-export-namespace-from": "7.8.3", "@babel/plugin-syntax-flow": "7.27.1", "@babel/plugin-syntax-import-assertions": "7.27.1", "@babel/plugin-syntax-import-attributes": "7.27.1", "@babel/plugin-syntax-import-meta": "7.10.4", "@babel/plugin-syntax-json-strings": "7.8.3", "@babel/plugin-syntax-jsx": "7.27.1", "@babel/plugin-syntax-logical-assignment-operators": "7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "7.8.3", "@babel/plugin-syntax-numeric-separator": "7.10.4", "@babel/plugin-syntax-object-rest-spread": "7.8.3", "@babel/plugin-syntax-optional-catch-binding": "7.8.3", "@babel/plugin-syntax-optional-chaining": "7.8.3", "@babel/plugin-syntax-private-property-in-object": "7.14.5", "@babel/plugin-syntax-top-level-await": "7.14.5", "@babel/plugin-syntax-typescript": "7.27.1", "@babel/plugin-syntax-unicode-sets-regex": "7.18.6", "@babel/plugin-transform-arrow-functions": "7.27.1", "@babel/plugin-transform-async-generator-functions": "7.28.0", "@babel/plugin-transform-async-to-generator": "7.27.1", "@babel/plugin-transform-block-scoped-functions": "7.27.1", "@babel/plugin-transform-block-scoping": "7.28.0", "@babel/plugin-transform-class-properties": "7.27.1", "@babel/plugin-transform-class-static-block": "7.27.1", "@babel/plugin-transform-classes": "7.28.0", "@babel/plugin-transform-computed-properties": "7.27.1", "@babel/plugin-transform-destructuring": "7.28.0", "@babel/plugin-transform-dotall-regex": "7.27.1", "@babel/plugin-transform-duplicate-keys": "7.27.1", "@babel/plugin-transform-duplicate-named-capturing-groups-regex": "7.27.1", "@babel/plugin-transform-dynamic-import": "7.27.1", "@babel/plugin-transform-explicit-resource-management": "7.28.0", "@babel/plugin-transform-exponentiation-operator": "7.27.1", "@babel/plugin-transform-export-namespace-from": "7.27.1", "@babel/plugin-transform-flow-strip-types": "7.27.1", "@babel/plugin-transform-for-of": "7.27.1", "@babel/plugin-transform-function-name": "7.27.1", "@babel/plugin-transform-json-strings": "7.27.1", "@babel/plugin-transform-literals": "7.27.1", "@babel/plugin-transform-logical-assignment-operators": "7.27.1", "@babel/plugin-transform-member-expression-literals": "7.27.1", "@babel/plugin-transform-modules-amd": "7.27.1", "@babel/plugin-transform-modules-commonjs": "7.27.1", "@babel/plugin-transform-modules-systemjs": "7.27.1", "@babel/plugin-transform-modules-umd": "7.27.1", "@babel/plugin-transform-named-capturing-groups-regex": "7.27.1", "@babel/plugin-transform-new-target": "7.27.1", "@babel/plugin-transform-nullish-coalescing-operator": "7.27.1", "@babel/plugin-transform-numeric-separator": "7.27.1", "@babel/plugin-transform-object-rest-spread": "7.28.0", "@babel/plugin-transform-object-super": "7.27.1", "@babel/plugin-transform-optional-catch-binding": "7.27.1", "@babel/plugin-transform-optional-chaining": "7.27.1", "@babel/plugin-transform-parameters": "7.27.7", "@babel/plugin-transform-private-methods": "7.27.1", "@babel/plugin-transform-private-property-in-object": "7.27.1", "@babel/plugin-transform-property-literals": "7.27.1", "@babel/plugin-transform-react-display-name": "7.28.0", "@babel/plugin-transform-react-jsx": "7.27.1", "@babel/plugin-transform-react-jsx-self": "7.27.1", "@babel/plugin-transform-react-jsx-source": "7.27.1", "@babel/plugin-transform-regenerator": "7.28.0", "@babel/plugin-transform-regexp-modifiers": "7.27.1", "@babel/plugin-transform-reserved-words": "7.27.1", "@babel/plugin-transform-runtime": "7.28.0", "@babel/plugin-transform-shorthand-properties": "7.27.1", "@babel/plugin-transform-spread": "7.27.1", "@babel/plugin-transform-sticky-regex": "7.27.1", "@babel/plugin-transform-template-literals": "7.27.1", "@babel/plugin-transform-typeof-symbol": "7.27.1", "@babel/plugin-transform-typescript": "7.28.0", "@babel/plugin-transform-unicode-escapes": "7.27.1", "@babel/plugin-transform-unicode-property-regex": "7.27.1", "@babel/plugin-transform-unicode-regex": "7.27.1", "@babel/plugin-transform-unicode-sets-regex": "7.27.1", "@babel/preset-env": "7.28.0", "@babel/preset-flow": "7.27.1", "@babel/preset-modules": "0.1.6-no-external-plugins", "@babel/preset-typescript": "7.27.1", "@babel/register": "7.27.1", "@babel/runtime": "7.27.6", "@babel/template": "7.27.2", "@babel/traverse": "7.28.0", "@babel/types": "7.28.0"}, "scripts": {"postinstall": "node utils/filePatch.js", "release": "node utils/release", "preinstall": "node utils/preinstall.js"}, "repository": {"type": "git", "url": "*************************:qrn/qrn-js.git"}, "author": "", "license": "ISC"}