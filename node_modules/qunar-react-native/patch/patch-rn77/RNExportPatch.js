/**
 * for RN63
 * rn63 版本已废弃, 此组件已移到@react-native-community中
 * 为兼容旧版写法, 此文件将重新指向到新库上
 */
const rn = require('react-native');
import { NativeModules } from 'react-native';
const patchList = [
    {
        name: 'CheckBox',
        newLib: () => require('@react-native-community/checkbox').default,
    },
    {
        name: 'Picker',
        newLib: () => require('@react-native-picker/picker').Picker,
    },
    {
        name: 'PickerIOS',
        newLib: () => require('@react-native-picker/picker').PickerIOS,
    },
    // {
    //     name: 'PickerHarmony',
    //     newLib: () => require('@react-native-picker/picker').PickerHarmony,
    // },
    { // TODO 原来是 68 内部 ？
        name: 'Slider',
        newLib: () => require('@react-native-community/slider').default,
    },
    {
        name: 'AsyncStorage',
        newLib: () => require('@react-native-async-storage/async-storage').default,
    },
    //
    // { //TODO 鸿蒙暂时不实现
    //     name: 'Clipboard',
    //     newLib: () => require('@react-native-clipboard/clipboard').default,
    // },
    {
        name: 'Clipboard',
        newLib: () => require('@react-native-community/clipboard').default,
    },
    { // TODO 原来是 68 内部 ？
        name: 'MaskedViewIOS',
        newLib: () => require('@react-native-community/masked-view').default,
    },
    {
        name: 'SegmentedControlIOS',
        newLib: () => require('@react-native-community/segmented-control').default,
    },
    {
        name: 'ART',
        newLib: () => require('@react-native-community/art'),
    },
    { // TODO 原来是 68 内部 ？
        name: 'ProgressViewIOS',
        newLib: () => require('@react-native-community/progress-view').ProgressView,
    },
    {
        name: 'DatePickerIOS',
        newLib: () => require('@react-native-community/datetimepicker').default,
    },
    // { // TODO 没找到能使用的地方，可能本次删除掉
    //     name: 'ImagePickerIOS',
    //     newLib: () => require('@react-native-community/image-picker-ios').default,
    // }
];

const { qrn_version } = NativeModules.QRCTDeviceInfo.getConstants() || {}

if (qrn_version.replace('v', '').split('.')[0] >= 5) {
    for (const obj of patchList) {
        const value = obj.newLib()
        Object.defineProperty(rn, obj.name, {
            value
        });
    }
}
