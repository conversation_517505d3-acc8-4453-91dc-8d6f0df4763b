// import React from 'react';
// import { Modal, View } from 'react-native';

// const AppContainer = require('AppContainer');
// const requireNativeComponent = require('requireNativeComponent');
// const RCTModalHostView = requireNativeComponent('RCTModalHostView', null);
// const I18nManager = require('I18nManager');
// const StyleSheet = require('StyleSheet');

// Modal.prototype.render = function () {
//     const containerStyles = {
//         backgroundColor: this.props.transparent ? 'transparent' : 'white',
//     };

//     let animationType = this.props.animationType;
//     if (!animationType) {
//         // manually setting default prop here to keep support for the deprecated 'animated' prop
//         animationType = 'none';
//         if (this.props.animated) {
//             animationType = 'slide';
//         }
//     }

//     let presentationStyle = this.props.presentationStyle;
//     if (!presentationStyle) {
//         presentationStyle = 'fullScreen';
//         if (this.props.transparent) {
//             presentationStyle = 'overFullScreen';
//         }
//     }

//     const innerChildren = __DEV__ ?
//         (<AppContainer rootTag={this.context.rootTag}>
//             {this.props.children}
//         </AppContainer>) :
//         this.props.children;

//     /**
//      * 此处主要是官方 Modal 组件的 render 方法里面通过 if...else... return 出来不同的
//      * 类型，导致 QAV 那边 xpath 获取出现问题，无法进行正确的数据埋点统计，故在外层包裹了一个
//      * 空白的 View
//      */
//     return (
//         <View>
//             {this.props.visible === false ? null :
//                 (<RCTModalHostView
//                     animationType={animationType}
//                     presentationStyle={presentationStyle}
//                     transparent={this.props.transparent}
//                     hardwareAccelerated={this.props.hardwareAccelerated}
//                     onRequestClose={this.props.onRequestClose}
//                     onShow={this.props.onShow}
//                     style={styles.modal}
//                     onStartShouldSetResponder={this._shouldSetResponder}
//                     supportedOrientations={this.props.supportedOrientations}
//                     onOrientationChange={this.props.onOrientationChange}
//                 >
//                     <View style={[ styles.container, containerStyles ]}>
//                         {innerChildren}
//                     </View>
//                 </RCTModalHostView>)
//             }
//         </View>
//     );
// }

// const side = I18nManager.isRTL ? 'right' : 'left';
// const styles = StyleSheet.create({
//     modal: {
//         position: 'absolute',
//     },
//     container: {
//         position: 'absolute',
//         [ side ]: 0,
//         top: 0,
//     }
// });