/**
 * for RN63
 * rn63 版本已废弃, 此组件已移到@react-native-community中
 * 为兼容旧版写法, 此文件将重新指向到新库上
 */
const rn = require('react-native');
import { NativeModules } from 'react-native';
const patchList = [
    {
        name: 'CheckBox',
        newLib: () => require('@react-native-community/checkbox').default,
    },
    {
        name: 'Picker',
        newLib: () => require('@react-native-community/picker').Picker,
    },
    {
        name: 'PickerIOS',
        newLib: () => require('@react-native-community/picker').PickerIOS,
    },
    // RN68 使用了 RN 内部的. 取消重定向
    // {
    //     name: 'Slider',
    //     newLib: () => require('@react-native-community/slider').default,
    // },
    {
        name: 'AsyncStorage',
        newLib: () => require('@react-native-community/async-storage').default,
    },
    {
        name: 'Clipboard',
        newLib: () => require('@react-native-community/clipboard').default,
    },
    // RN68 使用了 RN 内部的. 取消重定向
    // {
    //     name: 'MaskedViewIOS',
    //     newLib: () => require('@react-native-community/masked-view').default,
    // },
    {
        name: 'SegmentedControlIOS',
        newLib: () => require('@react-native-community/segmented-control').default,
    },
    {
        name: 'ART',
        newLib: () => require('@react-native-community/art'),
    },
    // RN68 使用了 RN 内部的. 取消重定向
    // {
    //     name: 'ProgressViewIOS',
    //     newLib: () => require('@react-native-community/progress-view').ProgressView,
    // },
    // RN68 使用了 RN 内部的. 取消重定向
    // {
    //     name: 'ProgressBarAndroid',
    //     newLib: () => require('@react-native-community/progress-bar-android').ProgressBar,
    // }
];
const { qrn_version } = NativeModules.QRCTDeviceInfo || {}
if(qrn_version.replace('v', '').split('.')[0] >= 5) {
    for (const obj of patchList) {
        const value = obj.newLib()
        Object.defineProperty(rn, obj.name, {
            value
        });
    }
}
