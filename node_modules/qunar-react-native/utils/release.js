var path = require('path');
var fs = require('fs');
var exec = require('child_process').exec;
var execSync = require('child_process').execSync;

var pkgPath = path.join(process.cwd(), './package.json');
var pkgConfig = {};
var pkgVerison; // pkg 版本号
var releaseVersion; // release 版本号

var BRANCH = 'release';

function getPkgInfo() {
    var pkg = fs.readFileSync(pkgPath, 'utf8');
    return JSON.parse(pkg);
}

// function getBranch() {
//     return new Promise(function (resolve, reject) {
//         exec('git describe --contains --all HEAD', function (err, branch) {
//             if (err) {
//                 reject('获取 branch 失败：' + err);
//             } else {
//                 resolve(branch.replace('\n', ''));
//             }
//         });
//     });
// }

function getRemote() {
    return new Promise(function (resolve, reject) {
        exec('git remote -v', function (err, remotes) {
            if (err) {
                reject('git 操作失败：' + err);
            } else {
                var remote = remotes.match(/(\w+)\s+.+gitlab.+/)[1];
                resolve(remote);
            }
        });
    })
}

new Promise(function (resolve, reject) {
    try {
        pkgConfig = getPkgInfo();
        pkgVerison = pkgConfig.version || '';
        resolve();
    } catch (e) {
        reject('读取 package.json 失败：' + e);
    }
}).then(function () {
    // return inquirer.prompt([{
    //     type: 'input',
    //     name: 'version',
    //     message: '请输入要发布的版本号: ',
    //     default: pkgVerison,
    //     validate: (content) => {
    //         if (/^\d+\.\d+\.\d+(?:-(.+))?$/.test(content) || !content) {
    //             return true;
    //         }
    //         return `版本号 ${content} 不合法，正确的格式应为: 1.0.2 或 2.3.0-beta.1`;
    //     }
    // }]);
    return new Promise(function (resolve, reject) {
        var params = process.argv.splice(2);
        var version = params.length > 0 ? params[0] : '';
        var reg = /^\d+\.\d+\.\d+(?:-(.+))?$/;

        if (!version) {
            reject('请加上版本号，比如：npm run release 1.0.0');
        } else if (!reg.test(version)) {
            reject(`版本号 ${version} 不合法，正确的格式应为: 1.0.2 或 2.3.0-beta.1`);
        } else {
            releaseVersion = version;

            exec('git tag -l', function (err, tagInfo) {
                if (!err && tagInfo) {
                    var tags = tagInfo.split('\n');
                }

                if (tags.indexOf('v' + releaseVersion) > -1) {
                    reject('已经存在该版本！')
                } else {
                    resolve();
                }
            });
        }
    });
}).then(function (data) {
    return new Promise(function (resolve, reject) {
        if (releaseVersion === pkgVerison) {
            resolve();
        } else {
            pkgConfig.version = releaseVersion;
            fs.writeFile(pkgPath, JSON.stringify(pkgConfig, null, 2), 'utf8', function (err) {
                if (err) {
                    return reject('更新 package.json 中的版本号失败：' + err);
                } else {
                    return resolve();
                }
            });
        }
    });
}).then(function (data) {
    return new Promise(function (resolve, reject) {
        getRemote().then(function (remote) {
            resolve({remote: remote, branch: BRANCH})
            // getBranch().then(function (branch) {
            //     resolve({ remote: remote, branch: branch });
            // }).catch(function (e) {
            //     reject(e);
            // })
        }).catch(function (e) {
            reject(e);
        })
    });
}).then(function (info) {
    return new Promise(function (resolve, reject) {
        try {
            if (releaseVersion !== pkgVerison) {
                execSync('git commit -am "[release]]Update Version to ' + releaseVersion + '"');
            }
            execSync('git pull');
            execSync('git tag v' + releaseVersion);
            execSync('git push ' + info.remote + ' ' + info.branch);
            execSync('git push ' + info.remote + ' v' + releaseVersion);
            resolve(info);
        } catch (e) {
            reject('git 操作失败：' + e);
        }
    });
}).then(function (info) {
    return new Promise(function (resolve, reject) {
        try {
            execSync('git checkout ' + info.branch + '-sdk');
            execSync('git pull');
            execSync('git merge ' + info.branch);

            var filePath = path.join(process.cwd(), './index.js');
            var codes = fs.readFileSync(filePath, 'utf8');
            var newCodes = codes.replace(/(\/\/)?.*get.*qextension\//g, function (str, $1) {
                return ($1 ? '' : '// ') + str;
            });
            fs.writeFileSync(filePath, newCodes, 'utf8');

            if (codes !== newCodes) {
                execSync('git commit -am "[release]]Update SDK Version to ' + releaseVersion + '"');
            }
            
            execSync('git push ' + info.remote + ' ' + info.branch + '-sdk');
            execSync('git tag v' + releaseVersion + '-sdk');
            execSync('git push ' + info.remote + ' v' + releaseVersion + '-sdk');
            execSync('git checkout ' + info.branch);
        } catch (e) {
            reject('发布 sdk 失败：' + e);
        }
    });
}).catch(function (msg) {
    console.error(msg);
});