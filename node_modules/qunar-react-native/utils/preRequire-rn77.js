// 预引用, 防止被 tree-shaking
module.exports = () => {
    require('../extension/FontLoader/FontLoader');
    require('react-native/Libraries/Image/AssetRegistry.js');
    require('react-native/Libraries/Core/Devtools/openURLInBrowser');
    require('@babel/runtime/helpers/interopRequireWildcard');
    require('react-native-webview');
    require('@react-native-community/cameraroll');
    require('@react-native-community/geolocation');
    require('@react-native-community/netinfo');
    require('deprecated-react-native-listview');
    require('deprecated-react-native-swipeable-listview');
    require('@react-native-community/viewpager/js/index.js');
    // require('react-native-pager-view').default;
    require('lodash');
    require('@react-native-community/checkbox').default;
    require('@react-native-community/picker').Picker;
    require('@react-native-community/picker').PickerIOS;
    require('@react-native-community/slider').default;
    require('@react-native-community/async-storage').default;
    require('@react-native-community/clipboard').default;
    require('@react-native-community/masked-view').default;
    require('@react-native-community/segmented-control').default;
    require('@react-native-community/art');
    require('@react-native-community/progress-view').ProgressView;
    require('@react-native-community/progress-bar-android').ProgressBar;
    require('react-native/Libraries/DeprecatedPropTypes/DeprecatedColorPropType.js');
    require('react-native/Libraries/DeprecatedPropTypes/deprecatedCreateStrictShapeTypeChecker.js');
    require('react-native/Libraries/DeprecatedPropTypes/DeprecatedEdgeInsetsPropType.js');
    require('react-native/Libraries/DeprecatedPropTypes/DeprecatedImagePropType.js');
    require('react-native/Libraries/DeprecatedPropTypes/DeprecatedImageSourcePropType.js');
    require('react-native/Libraries/DeprecatedPropTypes/DeprecatedImageStylePropTypes.js');
    require('react-native/Libraries/DeprecatedPropTypes/DeprecatedLayoutPropTypes.js');
    require('react-native/Libraries/DeprecatedPropTypes/DeprecatedPointPropType.js');
    require('react-native/Libraries/DeprecatedPropTypes/DeprecatedShadowPropTypesIOS.js');
    require('react-native/Libraries/DeprecatedPropTypes/DeprecatedStyleSheetPropType.js');
    require('react-native/Libraries/DeprecatedPropTypes/DeprecatedTextInputPropTypes.js');
    require('react-native/Libraries/DeprecatedPropTypes/DeprecatedTextPropTypes.js');
    require('react-native/Libraries/DeprecatedPropTypes/DeprecatedTextStylePropTypes.js');
    require('react-native/Libraries/DeprecatedPropTypes/DeprecatedTransformPropTypes.js');
    require('react-native/Libraries/DeprecatedPropTypes/DeprecatedViewAccessibility.js');
    require('react-native/Libraries/DeprecatedPropTypes/DeprecatedViewPropTypes.js');
    require('react-native/Libraries/DeprecatedPropTypes/DeprecatedViewStylePropTypes.js');
    require('react-native/Libraries/Utilities/deprecatedPropType.js');
};
