let fs = require('fs');
let path = require('path');
let childProcess = require('child_process');
let walk = require('./walk');
let copyAndReplace = require('./copyAndReplace');
const {getRNVersion} = require('./versionManager');

// 根据环境变量选择对应版本的 patch 文件夹
const targetRN = getRNVersion();
const patchFolderMap = {
    'RN68': 'filePatch-rn68',
    'RN72': 'filePatch-rn72',
    'RN77': 'filePatch-rn77',
}
const patchFolder = patchFolderMap[targetRN];
const srcPath = path.join(__dirname, `../${patchFolder}/`);
console.log(`当前使用的 patch 文件夹: ${patchFolder}`);

// 强制覆盖顶层 RN
const topRNPath = path.join(__dirname, '../../react-native'),
    theRNPath = path.join(__dirname, '../node_modules/react-native');

if (fs.existsSync(theRNPath)) {
    let cmd = `cp -R -f ${theRNPath}/* ${topRNPath}`;

    if (!fs.existsSync(topRNPath)) {
        cmd = `mkdir ${topRNPath} && ${cmd}`;
    }

    childProcess.execSync(cmd);
    childProcess.execSync(`rm -rf ${theRNPath}`);

    console.log('QRN 已强制覆盖 node_modules 顶层 RN 版本!');
    console.log(`${theRNPath} =======> ${topRNPath}`);
}

const RN_NODE_MODULES_PATH = path.join(topRNPath, './node_modules');
// 清空 TOP RN 的 node_modules, 目的是为了不新增依赖,引发依赖错误
if (fs.existsSync(RN_NODE_MODULES_PATH)) {
    childProcess.execSync(`rm -rf ${RN_NODE_MODULES_PATH}`);
    console.log('QRN 已强制删除 RN node_modules');
}

walk(srcPath).forEach((absoluteSrcFilePath) => {
    let relativeFilePath = path.relative(srcPath, absoluteSrcFilePath);

    copyAndReplace(absoluteSrcFilePath, path.resolve('../', dotFilePath(relativeFilePath)), {}, null);
    console.log('QRN 已完成文件 patch:', relativeFilePath);
});

function dotFilePath(p) {
    if (!p) return p;
    return p.replace('_patchnpmignore', '.npmignore')
        .replace('_patcheslintrc', '.eslintrc')
        // 模板文件替换名称
        .replace(/\.jspatch$/, '.js')
        .replace(/\.jsonpatch$/, '.json');
}
