/**
 * This script is run before the package is installed.
 */

const fs = require('fs');
const path = require('path');
const {getRNVersion} = require('./versionManager');

// 默认使用 RN68
const targetRN = getRNVersion();
console.log(`QRNJS 当前目标 RN 版本: ${targetRN}`);
const targetRNLowerCase = targetRN.toLowerCase();

if (targetRN !== 'RN68') {
    const targetPatchIndexPath = path.join(__dirname, '..', 'patch', 'index.js');
    if (fs.existsSync(targetPatchIndexPath)) {
        const patchContent = `import './patch-${targetRNLowerCase}/index.js';`
        fs.writeFileSync(targetPatchIndexPath, patchContent, 'utf8');
        console.log(`[${targetRN}] 已更新 patch/index.js`);
    }

    // 更新 preRequire.js
    const preRequireTemplatePath = path.join(__dirname, '..', `utils/preRequire-${targetRNLowerCase}.js`);

    const targetPreRequirePath = path.join(__dirname, '..', 'utils/preRequire.js');

    if (fs.existsSync(preRequireTemplatePath)) {
        const preRequireContent = fs.readFileSync(preRequireTemplatePath, 'utf8');
        fs.writeFileSync(targetPreRequirePath, preRequireContent, 'utf8');
        console.log(`[${targetRN}] 已更新 preRequire.js`);
    }
}
