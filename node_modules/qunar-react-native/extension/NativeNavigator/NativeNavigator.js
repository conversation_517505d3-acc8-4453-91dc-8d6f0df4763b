/**
 * @providesModule QNativeNavigator
 */

'use strict';

import { NativeModules } from 'react-native';
var QRCTNativeNavigator = NativeModules.QRCTNativeNavigator;

class NativeNavigator {
  /*
     get native VC Stacks

     @param callback: (groups: Array<String>) => void
   */
  static getPageStacks(callback) {
    QRCTNativeNavigator.getPageStacks(callback);
  }
}

module.exports = NativeNavigator;
