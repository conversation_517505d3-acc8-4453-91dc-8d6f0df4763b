/**
 * @providesModule MeasureText
 * @flow
 */
'use strict'

import { NativeModules } from 'react-native';
const QRCTMeasureText = NativeModules.QRCTMeasureText;

const MeasureText = {
    /**
   * 获取文字高度
   * @param options 传入属性参数，需要texts[array[string]]文字，widths[array[number]]宽度，fontSizes[array]字体大小，fontFamilies[array]格式，textPaddings[array[object]]文字距离左右边的空隙距离
   **/

   heights(options):Promise<object> {
     if(!options) {
       throw new Error("参数不能为空！");
     }

     if(!this.isFormat(options)) {
       throw new Error('参数异常，请检查后重新输入');
     }
     return  QRCTMeasureText.heights(options);
  }, isFormat(obj): boolean {
    const arr = Object.values(obj);
    const { texts, widths, fontSizes, fontFamilies, textPaddings } = obj;
    if(!(texts && widths && fontSizes && fontFamilies && textPaddings)) {
      return false;
    }
    const tArr = arr[0];
    var len = 0;
    if(!Array.isArray(tArr) || (len = tArr.length) === 0) {
      return false;
    }
    return !arr.some((v) => {
        if(!Array.isArray(v) || v.length !== len) return true;
    })
}

  /**
   * 获取文字宽度
   * @param options 传入属性参数，需要texts[array[string]]文字，height[number]高度，fontSize[number]字体大小，fontFamily[string]字体格式
   **/

// widths(options):Promise<object> {
//      return  QRCTMeasureText.widths(options);
//   },
    
};

module.exports = MeasureText;
