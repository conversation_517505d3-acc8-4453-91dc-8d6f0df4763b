/**
 * @providesModule FontLoader
 * @excludeModules QImageSet,QFontSet
 */

'use strict';

const { Platform, NativeModules } = require('react-native');
const nativeFontLoader = NativeModules.IconFontManager;
const QAV = NativeModules.QAV;

const platformFontSet = require('../../PlatformFontSet');

const isAndroid = Platform.OS === 'android';
const sendLoadFontLog = (loadFontSet) => {
    if (isAndroid && global.QrnConfig && global.QrnConfig['qrnPerformLoadFontsReportEnable'] === true) {
        let data = {
            bizTag: 'APP',
            bizType: 'app',
            module: 'default',
            appcode: 'qrn-js',
            page: 'IconFontManager',
            id: 'performLoadFonts',
            operType: 'monitor',
            ext: { loadFontSet: loadFontSet }
        };
        QAV && QAV.componentLog(data);
    }
};

if (platformFontSet && Object.keys(platformFontSet).length > 0) {
    sendLoadFontLog(platformFontSet);
    nativeFontLoader && nativeFontLoader.performLoadFonts(platformFontSet);
}

class FontLoader {
    static loadFontSet(fontSet) {
        // 如果是鸿蒙 跳过
        if (Platform.OS === 'harmony') {
            return;
        }

        if (fontSet && Object.keys(fontSet).length > 0) {
            sendLoadFontLog(fontSet);
            nativeFontLoader && nativeFontLoader.performLoadFonts(fontSet);
        }
    }
}

module.exports = FontLoader;
