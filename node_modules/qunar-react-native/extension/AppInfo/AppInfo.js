'use strict';

const QRCTAppInfo = require('react-native').NativeModules.QRCTAppInfo;
const appInfoConstant = QRCTAppInfo ? QRCTAppInfo.getConstants?.() : {};

module.exports = {
    getInfo: QRCTAppInfo ? QRCTAppInfo.getInfo : () => {},
    ...appInfoConstant,
    isTouristMode(callBack){
        if (QRCTAppInfo && QRCTAppInfo.isTouristMode) {
            QRCTAppInfo.isTouristMode(callBack);
        } else {
            callBack && callBack(false);
        }
    },

    currentMode(callBack){
        if (QRCTAppInfo && QRCTAppInfo.currentMode) {
            QRCTAppInfo.currentMode(callBack);
        } else {
            callBack && callBack('unknown');
        }
    }
};
