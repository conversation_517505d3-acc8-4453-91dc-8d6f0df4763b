/**
 *
 * @providesModule QVideoControl
 * @flow
 */
import React, { Component } from 'react';
import {requireNativeComponent,ViewPropTypes} from 'react-native';
import PropTypes from 'prop-types';

const propTypes = {
    ...ViewPropTypes,
    style: ViewPropTypes.style,
    initProps: PropTypes.object,
}

class QVideoControl extends Component {
    constructor(props) {
        super(props);
    }

    render() {
        const addtionProps = {
            ...this.props,
        };

        return (
            <QRCTVideoPlayerControl
                ref={(ref) => {
                }}
                {...addtionProps}

            />
        );
    }
}

QVideoControl.propTypes = propTypes;
QVideoControl.displayName = 'QVideoControl';

const QRCTVideoPlayerControl = requireNativeComponent('QRCTVideoPlayerControl', QVideoControl);

module.exports = QVideoControl;