/**
 * Copyright (c) 2013-present, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 *
 * @providesModule Switch
 * @flow
 */
'use strict';
import PropTypes from 'prop-types';
import { ViewPropTypes } from 'react-native';
var ColorPropType = require('deprecated-react-native-prop-types/DeprecatedColorPropType.js');
var NativeMethodsMixin = require('react-native/Libraries/Renderer/shims/NativeMethodsMixin');
var Platform = require('react-native').Platform;
var React = require('react');
var StyleSheet = require('react-native').StyleSheet;
var View = require('react-native').View;

var requireNativeComponent = require('react-native').requireNativeComponent;
const createReactClass = require('create-react-class');

type DefaultProps = {
    value: boolean,
    disabled: boolean,
};

/**
 * 通用开关组件
 *
 * @component Switch
 * @example ./Playground/js/Examples/SwitchExample.js[1-164]
 * @version >= 2.0.0
 * @description 渲染出一个通用的开关组件。这是一个受控组件，需要在 `onValueChange` 的回调中设置 `value`。
 * 渲染的效果 iOS 和 Android 是统一的。
 */
var Switch = createReactClass({
    propTypes: {
        ...ViewPropTypes,
        /**
         * @type bool
         * @property value
         * @description Switch 的状态值
         * @default false
         */
        value: PropTypes.bool,
        /**
         * @type bool
         * @property disabled
         * @description Switch 组件状态值是否可修改
         * @default false
         */
        disabled: PropTypes.bool,
        /**
         * @type function
         * @property onValueChange
         * @param {bool} value 当前修改值
         * @description (value) => {}
         */
        onValueChange: PropTypes.func,
        /**
         * Used to locate this view in end-to-end tests.
         */
        testID: PropTypes.string,
        /**
         * @deprecated
         * @type string
         * @property tintColor
         * @description 已废弃，关闭状态时的组件背景颜色, 建议使用 trackColor
         */
        tintColor: ColorPropType,
        /**
         * @type string
         * @property trackColor
         * @description 关闭状态时的组件背景颜色
         */
        trackColor: PropTypes.shape({
            false: ColorPropType,
            true: ColorPropType,
        }),
        /**
         * @deprecated
         * @type string
         * @property onTintColor
         * @description 已废弃，打开状态时的组件背景颜色, 建议使用 trackColor
         */
        onTintColor: ColorPropType,
        /**
         * @type string
         * @property thumbTintColor
         * @description 组件开关按钮的背景色
         */
        thumbTintColor: ColorPropType,
    },

    getDefaultProps: function (): DefaultProps {
        return {
            value: false,
            disabled: false,
        };
    },

    mixins: [NativeMethodsMixin],

    _rctSwitch: {},
    _onChange: function (event: Object) {
        if (Platform.OS === 'android') {
            this._rctSwitch.setNativeProps({ on: this.props.value });
        } else {
            this._rctSwitch.setNativeProps({ value: this.props.value });
        }
        //Change the props after the native props are set in case the props change removes the component
        this.props.onChange && this.props.onChange(event);
        this.props.onValueChange && this.props.onValueChange(event.nativeEvent.value);
    },

    render: function () {
        var props = { ...this.props };
        props.onStartShouldSetResponder = () => true;
        props.onResponderTerminationRequest = () => false;
        if (Platform.OS === 'android') {
            props.enabled = !this.props.disabled;
            props.on = this.props.value;
            props.style = [styles.rctSwitchAndroid, this.props.style];
            if (props.trackColor) {
                props.tintColor = props.trackColor.false;
                props.onTintColor = props.trackColor.true;
            }
        } else if (Platform.OS === 'ios') {
            props.style = [styles.rctSwitchIOS, this.props.style];
        }
        return (
            <RCTSwitch
                {...props}
                ref={(ref) => {
                    this._rctSwitch = ref;
                }}
                onChange={this._onChange}
            />
        );
    },
});

var styles = StyleSheet.create({
    rctSwitchIOS: {
        height: 31,
        width: 51,
    },
    rctSwitchAndroid: {
        height: 27,
        width: 47,
    },
});

if (Platform.OS === 'android') {
    var RCTSwitch = requireNativeComponent('QAndroidSwitch', Switch, {
        nativeOnly: { onChange: true, on: true, enabled: true },
    });
} else {
    var RCTSwitch = requireNativeComponent('RCTSwitch', Switch, {
        nativeOnly: { onChange: true },
    });
}

module.exports = Switch;
