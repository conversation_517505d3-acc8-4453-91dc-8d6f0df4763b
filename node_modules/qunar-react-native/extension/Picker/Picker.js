/**
 * Copyright (c) 2015-present, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 *
 * @providesModule QPicker
 * @flow
 */

'use strict';
import PropTypes from 'prop-types';
import {ViewPropTypes} from "react-native";
var ColorPropType = require('react-native').ColorPropType;
var PickerIOS = require('@react-native-community/picker').PickerIOS;
var Platform = require('react-native').Platform;
var QPickerAndroid;
if( Platform.OS === 'android') {
     QPickerAndroid = require('./PickerAndroid.js');
}
var React = require('react');
var StyleSheet = require('react-native').StyleSheet;
var StyleSheetPropType = require('deprecated-react-native-prop-types/DeprecatedStyleSheetPropType.js');
var TextStylePropTypes = require('deprecated-react-native-prop-types/DeprecatedTextStylePropTypes.js');
var UnimplementedView = require('react-native/Libraries/Components/UnimplementedViews/UnimplementedView.js');
var View = require('react-native').View;
var ViewStylePropTypes = require('deprecated-react-native-prop-types/DeprecatedViewStylePropTypes.js');

var itemStylePropType = StyleSheetPropType(TextStylePropTypes);

var pickerStyleType = StyleSheetPropType({
    ...ViewStylePropTypes,
    color: ColorPropType,
});

/**
 * 通用选择组件
 *
 * @component Picker
 * @example ./Playground/js/Examples/PickerExample.js[1-146]
 * @version >=1.4.0
 * @description 渲染出一个通用选择的组件。这是一个受控组件，需要在 `onValueChange` 的回调中设置 `selectedValue`。
 * 渲染的效果 iOS 和 Android 是统一的。
 *
 * ![Picker](./images/component-Picker-iOS.png)
 * ![Picker](./images/component-Picker-android.png)
 */
class QPicker extends React.Component {
    props: {
        style ?: $FlowFixMe,
        selectedValue ?: any,
        onValueChange ?: Function,
        itemStyle ?: $FlowFixMe,
        testID ?: string,
    };

    static propTypes = {
        ...ViewPropTypes,

        style: pickerStyleType,

        /**
         * 选中的数据值
         * @type any
         * @property selectedValue
         * @description Picker 中被选中的值
         */
        selectedValue: PropTypes.any,

        /**
         * 当选择的事件变化之后触发的回调
         *
         * @property onValueChange
         * @type function
         * @param {any}  selectedValue 当前选中值
         * @description (selectedValue)=> {}
         */
        onValueChange: PropTypes.func,

        /**
         * 可选择项的字体样式
         *
         * @property itemStyle
         * @type TextStylePropTypes
         * @description 选择项中的文本样式
         */
        itemStyle: itemStylePropType,
        /**
         * Used to locate this view in end-to-end tests.
         */
        testID: PropTypes.string,
    };

    render() {
        if (Platform.OS === 'ios') {
            // $FlowFixMe found when converting React.createClass to ES6
            return (
                      <View
                        onStartShouldSetResponder = {() => true}
                        onMoveShouldSetResponder = {()=>true}
                        onResponderTerminationRequest = {()=>false}
                        style = {this.props.style}
                      >
                        <PickerIOS {...this.props }>{this.props.children}</PickerIOS>
                      </View>);
        } else if (Platform.OS === 'android') {
            // $FlowFixMe found when converting React.createClass to ES6
                        return (
                      <View
                        onStartShouldSetResponder = {() => true}
                        onMoveShouldSetResponder = {()=>true}
                        onResponderTerminationRequest = {()=>false}
                        style = {this.props.style}
                      >
                        <QPickerAndroid {...this.props }>{this.props.children}</QPickerAndroid>
                      </View>);

        } else {
            return <UnimplementedView/> ;
        }
    }
}

/**
 * Individual selectable item in a QPicker.
 */
// $FlowFixMe found when converting React.createClass to ES6
QPicker.Item = class extends React.Component {
    props: {
        label: string,
        value ?: any,
        testID ?: string,
    };

    static propTypes = {
        /**
         * Text to display for this item.
         */
        label: PropTypes.string.isRequired,
        /**
         * The value to be passed to picker's `onValueChange` callback when
         * this item is selected. Can be a string or an integer.
         */
        value: PropTypes.any,
        /**
         * Used to locate the item in end-to-end tests.
         */
        testID: PropTypes.string,
    };

    render() {
        // The items are not rendered directly
        throw null;
    }
};

module.exports = QPicker;
