/**
 * @providesModule QPickerAndroid
 * @flow
 */

'use strict';
import PropTypes from 'prop-types';
import {ViewPropTypes} from "react-native";
var React = require('react');
var ReactChildren = React.Children;
var StyleSheet = require('react-native').StyleSheet;
var StyleSheetPropType = require('deprecated-react-native-prop-types/DeprecatedStyleSheetPropType.js');
var View = require('react-native').View;
var ViewStylePropTypes = require('deprecated-react-native-prop-types/DeprecatedViewStylePropTypes.js');
var TextStylePropTypes = require('deprecated-react-native-prop-types/DeprecatedTextStylePropTypes.js');

var itemStylePropType = StyleSheetPropType(TextStylePropTypes);
var processColor = require('react-native/Libraries/StyleSheet/processColor.js');
var requireNativeComponent = require('react-native').requireNativeComponent;

var {writingDirection, fontVariant, ...TextStyles} = TextStylePropTypes;

var pickerStyleType = StyleSheetPropType({
  ...ViewStylePropTypes,
  ...TextStyles,
});

type Event = Object;

class QPickerAndroid extends React.Component {

  static propTypes = {
    ...ViewPropTypes,
    style: pickerStyleType,
    selectedValue: PropTypes.any,
    onValueChange: PropTypes.func,
    itemStyle: itemStylePropType,
    testID: PropTypes.string,

    /**
     * Text related Props
     */
    ellipsizeMode: PropTypes.oneOf(['head', 'middle', 'tail', 'clip']),
    numberOfLines: PropTypes.number,
    selectable: PropTypes.bool,
    placeholder: PropTypes.string,
  };

  constructor(props, context) {
    super(props, context);
    var state = stateFromProps(props);

    this.state = {
      ...state,
      initialSelectedIndex: state.selectedIndex
    };
  }

  static getDerivedStateFromProps(nextProps) {
    return stateFromProps(nextProps);
  }

  render() {
    var nativeProps = {
      items: this.state.items,
      onSelect: this._onChange,
      selected: this.state.initialSelectedIndex,
      testID: this.props.testID,
      style: [styles.pickerAndroid, this.props.itemStyle, this.props.style],
    };

    return <Picker ref={(ref) => this._ref = ref} {...nativeProps} />;
  }

  _onChange = (event: Event) => {
    if (this.props.onValueChange) {
      var position = event.nativeEvent.position;
      if (position >= 0) {
        if (this.props.children && this.props.children[position]) {
          const value = this.props.children[position].props.value;
          this.props.onValueChange(value, position);
        }
      } else {
        this.props.onValueChange(null, position);
      }
    }
    this._lastNativePosition = event.nativeEvent.position;
    this.forceUpdate();
  };

  componentDidMount() {
    this._lastNativePosition = this.state.initialSelectedIndex;
  }

  componentDidUpdate() {
    // The picker is a controlled component. This means we expect the
    // on*Change handlers to be in charge of updating our
    // `selectedValue` prop. That way they can also
    // disallow/undo/mutate the selection of certain values. In other
    // words, the embedder of this component should be the source of
    // truth, not the native component.
    if (this._ref && this.state.selectedIndex !== this._lastNativePosition) {
      this._ref.setNativeProps({selected: this.state.selectedIndex});
      this._lastNativePosition = this.state.selectedIndex;
    }
  }
}

var styles = StyleSheet.create({
  pickerAndroid: {
    // The picker will conform to whatever width is given, but we do
    // have to set the component's height explicitly on the
    // surrounding view to ensure it gets rendered.
    // TODO would be better to export a native constant for this,
    // like in iOS the RCTDatePickerManager.m
    height: 200,
  },
});

var cfg = {
  nativeOnly: {
    items: true,
    selected: true,
  }
};

var Picker = requireNativeComponent('QPickerAndroid', QPickerAndroid, cfg);

// Translate prop and children into stuff that the native picker understands.
function stateFromProps(props) {
  var selectedIndex = 0;
  let items = ReactChildren.map(props.children, (child, index) => {
    if (child.props.value === props.selectedValue) {
      selectedIndex = index;
    }
    let childProps = {
      value: child.props.value,
      label: child.props.label,
    };
    if (child.props.color) {
      childProps.color = processColor(child.props.color);
    }
    return childProps;
  });
  return { selectedIndex, items };
};

module.exports = QPickerAndroid;
