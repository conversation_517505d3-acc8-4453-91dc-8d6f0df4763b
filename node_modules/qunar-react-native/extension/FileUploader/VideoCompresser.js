/**
 *
 * @providesModule VideoCompress
 */

'use strict'

import { NativeModules } from 'react-native';
const QRCTVideoCompresser = NativeModules.QRCTVideoCompresser;

const VideoCompress = {

     /**
   * 压缩视频
   * @param originUrlString 是原始视频的url
   * @param callback 是压缩成功的file返回值，取里面的compressUrlString字段
   * @param errorCallback 是原压缩失败的返回message
   **/
    compressVideo(originUrlString, callback, errorCallback) {
        QRCTVideoCompresser.compressVideo(originUrlString, callback, errorCallback);
    }
}

module.exports = VideoCompress;
