/**
 * Copyright (c) 2015-present, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 *
 * @providesModule AndroidRefreshableView
 * @flow
 */
'use strict';
import PropTypes from 'prop-types';
const React = require('react');
const requireNativeComponent = require('react-native').requireNativeComponent;
const createReactClass = require('create-react-class');
import ViewPropTypes from 'react-native'

var AndroidRefreshableView = createReactClass({
    propTypes:{
        ...ViewPropTypes,
        /**
         * 刷新时是否允许滑动
         *
         * @property scrollableDuringRefreshing
         * @type bool
         * @description LoadControl、RefreshControl触发刷新时是否允许滑动
         */
        scrollableDuringRefreshing: PropTypes.bool,
        /**
         * 用于用户手动触发list的refreshing
         * @type bool
         */
        manualRefreshing: PropTypes.bool,
        /**
         * 用于用户手动触发list的loading
         * @type bool
         */
        manualLoading: PropTypes.bool,
    },
    startRefreshing(){
        this._nativeRef && this._nativeRef.setNativeProps && this._nativeRef.setNativeProps({manualRefreshing:true});
    },
    startLoading(){
        this._nativeRef && this._nativeRef.setNativeProps && this._nativeRef.setNativeProps({manualLoading:true});
    },
    render: function() {
      return <RefreshableView
      ref={ref => this._nativeRef = ref}
      style={{
       flexGrow: 1,
       flexShrink: 1,
      }}
      {...this.props} />;
    },
});
const RefreshableView = requireNativeComponent('AndroidRefreshableView', AndroidRefreshableView);

module.exports = AndroidRefreshableView;
