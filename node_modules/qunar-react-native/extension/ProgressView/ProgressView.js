/**
 * @providesModule QProgressView
 * @flow
 */

'use strict';

import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { StyleSheet, View, Image, Platform } from 'react-native';

const defualtTrackHeight = 2;

/**
 * 进度条
 * @component ProgressView
 * @example ./Playground/js/Examples/ProgressViewExample.js[1-59]
 * @version >=v1.0.0
 * @description 渲染一个进度条
 *
 * ![ProgressView](./images/component-ProgressView.png)
 */
class ProgressView extends Component {
    constructor(props){
        super(props);

        this.state = {
            visible: false,
            trackWidth: 0,
            progressWidth: 0,
        };
    }

    static getDerivedStateFromProps(nextProps, prevState) {
        if (prevState.visible) {
            return {
                progressWidth: ProgressView.calculateProgressWidth(nextProps.progress, prevState.trackWidth)
            };
        }

        return null;
    }

    render() {
        const {progressImage, progressTintColor, progressViewStyle, trackImage, trackTintColor} = this.props;
        const {progressWidth, trackWidth} = this.state;
        const
            trackStyle = {
                backgroundColor: trackTintColor,
                borderRadius: progressViewStyle === 'bar' ? 0 : defualtTrackHeight,
            },
            progressStyle = {
                width: progressWidth,
                backgroundColor: progressTintColor,
                borderRadius: progressViewStyle === 'bar' ? 0 : defualtTrackHeight,
            },
            trackImageStyle = {
                width: trackWidth
            },
            progressImageStyle = {
                width: progressWidth
            };

        return (
            <View style={[styles.track, trackStyle]} ref="track" onLayout={(event) => this.initLayout(event)}>
                {trackImage ? <Image style={[styles.img, trackImageStyle]} source={trackImage}/> : null}
                <View style={[styles.progress, progressStyle]}>
                    {progressImage ? <Image style={[styles.img, progressImageStyle]} source={progressImage}/> : null}
                </View>
            </View>
        );
    }

    initLayout(e) {
        if(!this.state.visible){
            const progress = this.props.progress;
            const trackWidth = e.nativeEvent.layout.width;

            this.setState({
                trackWidth,
                progressWidth: ProgressView.calculateProgressWidth(progress, trackWidth),
                visible: true,
            });
        }
    }

    /**
     * 计算进度条长度
     * @param progress
     * @param trackWidth
     * @return {number}
     */
    static calculateProgressWidth(progress, trackWidth) {
        if (progress > 1) {
            progress = 1;
        } else if (progress < 0) {
            progress = 0;
        }

        return progress * trackWidth;
    }
}

ProgressView.defaultProps = {
    progress: 0,
    progressTintColor: '#0b6aff',
    trackTintColor: '#b6b6b6',
};

ProgressView.propTypes = {
    /**
     * @property progress
	 * @type number
	 * @description 当前的进度值（0到1之间）。
     */
    progress: PropTypes.number,

    /**
     * @property progressImage
	 * @type Image.propTypes.source
	 * @description 一个可以拉伸的图片，用于显示进度条。
     */
    progressImage: (Platform.OS === 'ios') ? Image.propTypes.source : PropTypes.any,

    /**
     * @property progressTintColor
	 * @type string
	 * @description 进度条本身染上的颜色。
     */
    progressTintColor: PropTypes.string,

    /**
     * @property progressViewStyle
	 * @type enum('default', 'bar')
	 * @description 进度条的样式。
     */
    progressViewStyle: PropTypes.oneOf(['default', 'bar']),

    /**
     * @property trackImage
	 * @type Image.propTypes.source
	 * @description 一个可拉伸的图片，用于显示进度条后面的轨道。
     */
    trackImage: (Platform.OS === 'ios') ? Image.propTypes.source : PropTypes.any,

    /**
     * @property trackTintColor
	 * @type string
	 * @description 进度条轨道染上的颜色。
     */
    trackTintColor: PropTypes.string,
};

const styles = StyleSheet.create({
    track: {
        alignSelf: 'stretch',
        height: defualtTrackHeight,
    },
    progress: {
        height : defualtTrackHeight,
    },
    img: {
        position: 'absolute',
        top: 0,
        left: 0,
        resizeMode: 'stretch',
        height: defualtTrackHeight
    },
});

module.exports = ProgressView;
