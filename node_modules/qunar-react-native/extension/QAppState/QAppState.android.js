'use strict';

const NativeEventEmitter = require('react-native').NativeEventEmitter;
import QNativeAppState from './QNativeAppState';

const logError = require('react-native/Libraries/Utilities/logError');
const invariant = require('fbjs/lib/invariant');

const EventEmitter = require('react-native/Libraries/vendor/emitter/EventEmitter');

/**
 * `QAppState` can tell you if the app is in the foreground or background,
 * and notify you when the state changes.
 *
 * See http://facebook.github.io/react-native/docs/appstate.html
 */

class QAppState extends NativeEventEmitter {
  _eventHandlers: Object;
  currentState: ?string;
  isAvailable: boolean = true;

  constructor() {
    super(QNativeAppState);

    this.isAvailable = true;
    this._eventHandlers = {
      change: new Map(),
      memoryWarning: new Map(),
    };

    let eventUpdated = false;

    this.currentState = QNativeAppState.getConstants().initialQAppState;

    // TODO: this is a terrible solution - in order to ensure `currentState` prop
    // is up to date, we have to register an observer that updates it whenever
    // the state changes, even if nobody cares. We should just deprecate the
    // `currentState` property and get rid of this.
    this.addListener(
        'foregroundDidChange',
        (appStateData) => {
          eventUpdated = true;
          this.currentState = appStateData.app_state;
        }
    );

    // TODO: see above - this request just populates the value of `currentState`
    // when the module is first initialized. Would be better to get rid of the prop
    // and expose `getCurrentAppState` method directly.
    QNativeAppState.getCurrentAppState(
        (appStateData) => {
          // It's possible that the state will have changed here & listeners need to be notified
          if (!eventUpdated && this.currentState !== appStateData.app_state) {
            this.currentState = appStateData.app_state;
            this.emit('foregroundDidChange', appStateData);
          }
        },
        logError
    );
  }

  // TODO: now that AppState is a subclass of NativeEventEmitter, we could
  // deprecate `addEventListener` and `removeEventListener` and just use
  // addListener` and `listener.remove()` directly. That will be a breaking
  // change though, as both the method and event names are different
  // (addListener events are currently required to be globally unique).
  /**
   * Add a handler to AppState changes by listening to the `change` event type
   * and providing the handler.
   *
   * See http://facebook.github.io/react-native/docs/appstate.html#addeventlistener
   */
  addEventListener(
      type: string,
      handler: Function
  ) {
    invariant(
        ['change', 'memoryWarning'].indexOf(type) !== -1,
        'Trying to subscribe to unknown event: "%s"', type
    );
    if (type === 'change') {
      this._eventHandlers[type].set(handler, this.addListener(
          'foregroundDidChange',
          (appStateData) => {
            handler(appStateData.app_state);
          }
      ));
    } else if (type === 'memoryWarning') {
      this._eventHandlers[type].set(handler, this.addListener(
          'memoryWarning',
          handler
      ));
    }
  }

  /**
   * Remove a handler by passing the `change` event type and the handler
   */
  removeEventListener(
      type: string,
      handler: Function
  ) {
    invariant(
        ['change', 'memoryWarning'].indexOf(type) !== -1,
        'Trying to remove listener for unknown event: "%s"', type
    );
    if (!this._eventHandlers[type].has(handler)) {
      return;
    }
    this._eventHandlers[type].get(handler).remove();
    this._eventHandlers[type].delete(handler);
  }
}

function throwMissingNativeModule() {
  invariant(
      false,
      'Cannot use AppState module when native RCTAppState is not included in the build.\n' +
      'Either include it, or check AppState.isAvailable before calling any methods.',
  );
}

class MissingNativeAppStateShim extends NativeEventEmitter {
  // AppState
  isAvailable: boolean = false;
  currentState: ?string = null;

  addEventListener() {
    throwMissingNativeModule();
  }

  removeEventListener() {
    throwMissingNativeModule();
  }

  // EventEmitter
  addListener() {
    throwMissingNativeModule();
  }

  removeAllListeners() {
    throwMissingNativeModule();
  }

  removeSubscription() {
    throwMissingNativeModule();
  }
}

// This module depends on the native `RCTAppState` module. If you don't include it,
// `AppState.isAvailable` will return `false`, and any method calls will throw.
// We reassign the class variable to keep the autodoc generator happy.
if (QNativeAppState) {
  QAppState = new QAppState();
} else {
  QAppState = new MissingNativeAppStateShim();
}

module.exports = QAppState;
