import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { processColor, requireNativeComponent, PointPropType, View, ViewPropTypes } from 'react-native';
import deprecatedPropType from 'react-native/Libraries/Utilities/deprecatedPropType.js';

const convertPointWithFabric = (name, point) => {
    if (Array.isArray(point)) {
        console.warn(
            `LinearGradient '${name}' property shoule be an object with fields 'x' and 'y', ` +
                'Array type is deprecated.'
        );

        return {
            x: point[0],
            y: point[1],
        };
    }
    if (name === 'start' && point === undefined) {
        return {
            x: 0,
            y: 0,
        };
    }
    if (name === 'end' && point === undefined) {
        return {
            x: 0,
            y: 1,
        };
    }
    return point;
};

const convertPoint = (name, point) => {
    if (global.__isFabric === true) {
        return convertPointWithFabric(name, point);
    }

    return oldConvertPoint(name, point);
};

const oldConvertPoint = (name, point) => {
    if (Array.isArray(point)) {
        console.warn(
            `LinearGradient '${name}' property shoule be an object with fields 'x' and 'y', ` +
                'Array type is deprecated.'
        );

        return {
            x: point[0],
            y: point[1],
        };
    }
    if (name === 'start' && point === undefined) {
        return {
            x: 0,
            y: 0,
        };
    }
    if (name === 'end' && point === undefined) {
        return {
            x: 0,
            y: 1,
        };
    }
    return point;
};

type PropsType = {
    start?: Array<number> | { x: number, y: number },
    end?: Array<number> | { x: number, y: number },
    colors: Array<string>,
    locations?: Array<number>,
} & typeof View;

export default class QLinearGradient extends Component {
    static propTypes = {
        start: PropTypes.oneOfType([
            PointPropType,
            deprecatedPropType(PropTypes.arrayOf(PropTypes.number), 'Use point object with {x, y} instead.'),
        ]),
        end: PropTypes.oneOfType([
            PointPropType,
            deprecatedPropType(PropTypes.arrayOf(PropTypes.number), 'Use point object with {x, y} instead.'),
        ]),
        colors: PropTypes.arrayOf(PropTypes.string).isRequired,
        locations: PropTypes.arrayOf(PropTypes.number),
        ...ViewPropTypes,
    };
    props: PropsType;
    gradientRef: any;


    setNativeProps(props: PropsType) {
        this.gradientRef.setNativeProps(props);
    }

    render() {
        const { start, end, colors, locations, ...otherProps } = this.props;
        if (colors && locations && colors.length !== locations.length) {
            console.warn('LinearGradient colors and locations props should be arrays of the same length');
            return null;
        }

        // colors 至少要设置两种颜色
        if (!Array.isArray(colors) || colors.length === 0 || colors.length === 1) {
            console.warn('LinearGradient colors length must be more than two');
            return null;
        }

        // start=end的时候打警告处理
        if (start && end && start.x === end.x && start.y === end.y) {
            console.warn('LinearGradient start should not equal to end');
            return null;
        }

        //locations的范围是 0~1
        var isLocationsCorrect = true;
        if (Array.isArray(locations)) {
            locations.forEach((v) => {
                if (v < 0 || v > 1) {
                    isLocationsCorrect = false;
                }
            });
        }
        if (!isLocationsCorrect) {
            console.warn('LinearGradient locations object must be 0~1');
            return null;
        }

        return (
            <QLinearGradientIos
                ref={(component) => {
                    this.gradientRef = component;
                }}
                {...otherProps}
                startPoint={convertPoint('start', start)}
                endPoint={convertPoint('end', end)}
                colors={colors.map(processColor)}
                locations={locations ? locations.slice(0, colors.length) : null}
            />
        );
    }
}
var QLinearGradientIos = requireNativeComponent('QRCTLinearGradient', QLinearGradient);

module.exports = QLinearGradient;
