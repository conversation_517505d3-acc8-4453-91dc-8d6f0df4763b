'use strict';

import React, {Component } from 'react';
import PropTypes from 'prop-types';

import {
    StyleSheet,
    View,
    Text,
    requireNativeComponent,
    NativeModules,
    ViewPropTypes,
} from 'react-native';

import ColorConfig from '../../ColorConfig.js';

const NOTICE = 'noticeContent';
const NOMORE = 'noMoreContent';
const LOADING = 'loadingContent';
const REMOVED_PROPS = ['isLoading', 'noMore', 'loadComplete']

const nativeFontLoader = NativeModules.IconFontManager;
nativeFontLoader.performLoadFonts({
	"qunar_react_native": "http://s.qunarzz.com/qunar_react_native/font/1.0.1/qunar_react_native.ttf",
});

/**
 * 上拉加载组件
 *
 * @component NativeLoadControl
 * @skip
 */
class LoadControl extends Component {

    constructor(props) {
        super(props);
        this.loading = false;
        this._nativeRef = null;
        this.loadState = 'stop';
        this.state = {
            iconMessage:'',
            infoMessage: this.props[NOTICE]
        }
    }

    stopLoading(config = {}) {
        if (this.loading) {
            this._nativeRef && this._nativeRef.setNativeProps && this._nativeRef.setNativeProps({loadComplete: true});
        }
    }

    startLoading(config = {}) {
        if (!this.loading && !this.props.noMore) {
            this._nativeRef.setNativeProps({isLoading:true});
        }
    }

    _onLoadStateChange(info) {
    	switch (info.nativeEvent.state) {
    		case "loading":
				if(!this.props.noMore) {
					this.loading = true;
	    			this.setState({iconMessage: this.props.loadingIcon, infoMessage: this.props[LOADING]});
	                this.props.onLoad && this.props.onLoad();
				}
                break;

    		case "stop":
                this.loading = false;
    			this.setState({iconMessage: '', infoMessage: this.props[NOTICE]});

				if(this.props.noMore) {
					this.setState({iconMessage: '', infoMessage: this.props[NOMORE]});
				}
                break;

    		case "noMore":
    			this.setState({iconMessage: '', infoMessage: this.props[NOMORE]});
                break;

    		default:
    	}
    }

    _onPressLoadButton(){
        this._nativeRef.setNativeProps({isLoading:true});
    }
    //
    // componentWillReceiveProps (nextProps) {
    //     let props = {};
    //     Object.keys(nextProps).map((propName) => {
    //         if(REMOVED_PROPS.indexOf(propName) > -1) {
    //             this._nativeRef
    //                 && this._nativeRef.setNativeProps
    //                 && this._nativeRef.setNativeProps({[propName]: nextProps[propName]});
    //         }
    //     })
    // }

    // TODO 需要验证（验证没问题后，可删除被注释的componentWillReceiveProps）
    componentDidUpdate(prevProps) {
        const nextProps = this.props;

        REMOVED_PROPS.forEach(propName => {
            // props中是否存在propsName属性
            const propsIsExist = nextProps.hasOwnProperty(propName);

            // 属性存在，且发生改变时调用setNativeProps
            if (propsIsExist && prevProps[propName] !== nextProps[propName]) {
                this._nativeRef
                && this._nativeRef.setNativeProps
                && this._nativeRef.setNativeProps({[propName]: nextProps[propName]});
            }
        });
    }

    render() {
		// 直接传入 noMore 等属性会有问题，要在 componentWillReceiveProps 中用 setNativeProps 方式设置
        let props = {};
        Object.keys(this.props).map((propName) => {
            if(REMOVED_PROPS.indexOf(propName) === -1) {
                props[propName] = this.props[propName];
            }
        });

        return (
            <QRNLoadMoreControlView
                {...props}
                ref={ref => this._nativeRef = ref}
                onLoadStateChange={this._onLoadStateChange.bind(this)}
                onPressLoadButton={this._onPressLoadButton.bind(this)}
                style={[styles.contentContaienr, {height: props.height}]}
                >
                <Text style={[styles.icon, props.iconStyle]}>{this.state.iconMessage}</Text>
                <Text style={[styles.content, props.textStyle]}>{this.state.infoMessage}</Text>
            </QRNLoadMoreControlView>

        );
    }
}

 LoadControl.propTypes = {
     ...ViewPropTypes,
     /**
      * 高度
      *
      * @property height
   * @type number
      * @default 35
   * @description LoadControl的高度
      */
     height: PropTypes.number,
     /**
      * 没有更多
      *
      * @property noMore
   * @type bool
      * @default false
   * @description 如果为true，则显示 `NOMORE` 状态
      */
     noMore: PropTypes.bool,
     /**
      * 提示文本
      *
      * @property noticeContent
   * @type string
      * @default '上拉加载更多'
   * @description 显示在最下方的提示文本
      */
     noticeContent: PropTypes.string,
     /**
      * 加载文本
      *
      * @property loadingContent
   * @type string
      * @default '努力加载中'
   * @description 加载时的文本
      */
     loadingContent: PropTypes.string,
     /**
      * 加载图标
      *
      * @property loadingIcon
   * @type string
      * @default '\uf089'
   * @description 加载时旋转的图标
      * @version >=1.3.0
      */
     loadingIcon: PropTypes.string,
     /**
      * 没有更多文本
      *
      * @property noMoreContent
   * @type string
      * @default '没有更多了'
   * @description 没有更多时显示的文本
      */
     noMoreContent: PropTypes.string,
     /**
      * 刷新事件
      *
      * @property onLoad
      * @type function
   * @description
      *
      * 变成正在加载时触发的事件
      */
     onLoad: PropTypes.func,
     /**
      * 加载结果
      *
      * @property loadComplete
   * @type bool
      * @default false
   * @description
   *
   * 通知native加载结果，native停止刷新
      */
     loadComplete: PropTypes.bool,
     /**
      * 点击事件
      *
      * @property onPress
      * @type function
   * @description
      *
      * 点击LoadControl时触发的事件
      */
     onPress: PropTypes.func,
     /**
      * 样式
      *
      * @property style
   * @type ViewPropTypes
   * @description LoadControl样式
      */
     style: ViewPropTypes.style,
     /**
      * 文本样式
      *
      * @property textStyle
      * @type Text.propTypes.style
      * @description LoadControl的文本样式
      * @version >=1.3.0
      */
     textStyle: Text.propTypes.style,
     /**
      * 按钮样式
      *
      * @property iconStyle
      * @type Text.propTypes.style
      * @description LoadControl的按钮样式
      * @version >=1.3.0
      */
     iconStyle: Text.propTypes.style,
     onPressLoadButton: PropTypes.func,
     onLoadStateChange: PropTypes.func,
     isLoading: PropTypes.bool,
     hidden: PropTypes.bool,
 };

LoadControl.defaultProps = {
    height: 35,
    noMore: false,
    loadState: 'stop',
    iconMessage: '',
    noticeContent: '上拉加载更多',
    loadingContent: '努力加载中',
    loadingIcon: '\uf089',
    noMoreContent: '没有更多了'
};

const QRNLoadMoreControlView = requireNativeComponent('QRNLoadMoreControlView', LoadControl);

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        left: 0,
        right: 0,
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
        borderLeftWidth: 1,
        borderRightWidth: 1,
        borderColor: 'transparent',
    },
    contentContaienr: {
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
    },
    content: {
        backgroundColor: "transparent",
        color: ColorConfig['blue-main'],
        fontSize: 14,
    },
    icon: {
        backgroundColor: "transparent",
        color: ColorConfig['blue-main'],
        fontSize: 16,
        fontFamily: 'qunar_react_native',
        marginRight: 3,
    }
});
module.exports = LoadControl;
