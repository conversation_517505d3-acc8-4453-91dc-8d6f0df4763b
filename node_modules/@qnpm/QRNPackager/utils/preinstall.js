/**
 * This script is run before the package is installed.
 */

const fs = require('fs');
const path = require('path');
const {getRNVersion} = require('./versionManager');

// 默认使用 RN68
const targetRN = getRNVersion();
console.log(`当前目标 RN 版本: ${targetRN}`);
const targetRNLowerCase = targetRN.toLowerCase();

// 非 RN68 时需要合并 qrn.metro.config.js
if (targetRN !== 'RN68') {
    // 更新 qrn.metro.config.js
    const templateMetroConfigPath = path.join(__dirname, '..', `qrn.metro.config-${targetRNLowerCase}.js`);
    const rootMetroConfigPath = path.join(__dirname, '..', 'qrn.metro.config.js');
    if (fs.existsSync(templateMetroConfigPath)) {
        const templateContent = fs.readFileSync(templateMetroConfigPath, 'utf8');
        fs.writeFileSync(rootMetroConfigPath, templateContent, 'utf8');
        console.log(`[${targetRN}] 已更新 qrn.metro.config.js`);
    }
}
