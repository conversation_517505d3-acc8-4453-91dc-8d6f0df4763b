/*
 * <EMAIL>
 * 分析一个http或者https的link的TTF文件里面的FontFamily
 */

const fs = require('fs');
const http = require('http');
const https = require('https');
const url = require('url');

const HTTP_Protocol = 'http:';
const HTTPS_Protocol = 'https:';
const Promise = require('promise');


const TABLE_COUNT_OFFSET = 4,
      TABLE_HEAD_OFFSET = 12,
      TABLE_HEAD_SIZE = 16,
      TAG_OFFSET = 0,
      TAG_SIZE = 4,
      CHECKSUM_OFFSET = TAG_OFFSET + TAG_SIZE,
      CHECKSUM_SIZE = 4,
      CONTENTS_PTR_OFFSET = CHECKSUM_OFFSET + CHECKSUM_SIZE,
      CONTENTS_PTR_SIZE = 4;

function offset(data, name) {
    const numTables = data.readUInt16BE(TABLE_COUNT_OFFSET);

    for (let i = 0; i < numTables; ++i) {
        let o = TABLE_HEAD_OFFSET + i * TABLE_HEAD_SIZE,
            tag = data.slice(o, o + CONTENTS_PTR_SIZE).toString();

        if (tag === name) {
            return data.readUInt32BE(o + CONTENTS_PTR_OFFSET);
        }
    }
}
function getNames(data) {
    let ntOffset = offset(data, 'name'),
        offsetStorage = data.readUInt16BE(ntOffset+4),
        numberNameRecords = data.readUInt16BE(ntOffset+2);

    let storage = offsetStorage + ntOffset;

    let info = {};
    for (let j = 0; j < numberNameRecords; j++) {
      let o = ntOffset + 6 + j*12;

      let platformId = data.readUInt16BE(o),
          nameId = data.readUInt16BE(o+6),
          stringLength = data.readUInt16BE(o+8),
          stringOffset = data.readUInt16BE(o+10);

      if (!info[nameId]) {
        info[nameId] = '';

        for (let k = 0; k < stringLength; k++) {
            let charCode = data[storage+stringOffset+k];
            if (charCode === 0) continue;
            info[nameId] += String.fromCharCode(charCode);
        }
      }
    }

    return info["1"]; // name中的1是fontFamily，参看TTF格式标准。
}
module.exports = function(urlString) {
    const fontUrl = url.parse(urlString);
    if ([HTTPS_Protocol, HTTP_Protocol].indexOf(fontUrl.protocol) == -1)
        return Promise.reject(new Error(`无效的font地址,${urlString}不是一个有效的http或者https地址。`));

    return new Promise((resolve, reject) => {
        const req = fontUrl.protocol === HTTPS_Protocol ? https : http;
        req.get(urlString, (res) => {
            const buffers = [];
            res.on('data', (chunk) => {
                buffers.push(chunk);
            });
            res.on('end', () => {
                resolve(Buffer.concat(buffers))
            })
        }).on('error', (e) => {
            reject(new Error(`无法从${urlString}下载字体文件，请检查网络或者地址配置。${e.stack}`));
        });
    }).then((fontBuffer) => {
        return getNames(fontBuffer);
    });
};
