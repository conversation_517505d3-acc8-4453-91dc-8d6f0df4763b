/**
 * Copyright (c) 2015-present, Facebook, Inc.
 * All rights reserved.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 */
'use strict';

const signedsource = require('./signedsource');
const util = require('util');
// 编译结束，签名
const signTemplates = {
    'bundle_all': {
        'hasBuildTag':  '\n__SSTOKENSTRING = "@%s %s";\n__BTAG = "%s";\n__QP_INFO = %s;\n',
        'noBuildTag':   '\n__SSTOKENSTRING = "@%s %s";\n__QP_INFO = %s;\n'
    },
    'biz': {
        'hasBuildTag':  '\n__SSTOKENSTRING_BIZ = "@%s %s";\n__BTAG_BIZ = "%s";\n__QP_INFO = %s;\n',
        'noBuildTag':   '\n__SSTOKENSTRING_BIZ = "@%s %s";\n__QP_INFO = %s;\n'
    },
    'platform': {
        'hasBuildTag':  '\n__SSTOKENSTRING_PLT = "@%s %s";\n__BTAG_PLT = "%s";\n',
        'noBuildTag':   '\n__SSTOKENSTRING_PLT = "@%s %s";\n'
    },
    'default': {
        'hasBuildTag':  '\n__SSTOKENSTRING = "@%s %s";\n__BTAG = "%s";\n__QP_INFO = %s;\n',
        'noBuildTag':   '\n__SSTOKENSTRING = "@%s %s";\n__QP_INFO = %s;\n'
    }
};
function sign(source, options) {
    const ssToken = util.format('<<%sSource::*O*zOeWoEQle#+L!plEphiEmie@IsG>>', 'Signed');
    const bundleType = options.bundleType;
    const buildTag = options.buildTag;
    const qpInfo = options.qpInfo;

    const hasBuildTag = !!buildTag && (typeof buildTag === 'string') && buildTag.length > 0;

    const tmpSet = hasBuildTag ? signTemplates.hasBuildTag : signTemplates.noBuildTag;

    const signTmp = signTemplates.hasOwnProperty(bundleType) ? signTemplates[bundleType] : signTemplates["default"];
    // const singText = hasBuildTag ?
    //                 util.format(signTmp.hasBuildTag, 'generated', ssToken, buildTag, qpInfo) :
    //                 util.format(signTmp.noBuildTag, 'generated', ssToken, qpInfo)
    let singText = '';
    if (hasBuildTag) {
        if (bundleType === 'platform') {
            singText = util.format(signTmp.hasBuildTag, 'generated', ssToken, buildTag);
        } else {
            singText = util.format(signTmp.hasBuildTag, 'generated', ssToken, buildTag, qpInfo);
        }
    } else {
        if (bundleType === 'platform') {
            singText = util.format(signTmp.noBuildTag, 'generated', ssToken);
        } else {
            singText = util.format(signTmp.noBuildTag, 'generated', ssToken, qpInfo);
        }
    }

    return signedsource.sign(source + singText).signed_data;
}

module.exports = sign;
