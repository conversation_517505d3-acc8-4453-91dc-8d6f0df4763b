/**
 * <AUTHOR>
 * @description QRN PACK TOOL
 *
 * 本工具包含两个主要功能：
 * 1. QRN 特有的 PACK 逻辑：校验配置文件、注入签名、生成 staticAssets.json
 * 2. 定制的 RN 原生 PACK 逻辑：构建 JS & SOURCEMAP 产物
 */

const Promise = require('promise');
const path = require('path');
const chalk = require('chalk');
const duties = require('./duties.js');
const utils = require('./utils');

// React Native CLI Bundle 命令参数配置
const BUNDLE_COMMAND_ARGS = [
  {
    name: '--entry-file <path>',
    description: 'Path to the root JS file, either absolute or relative to JS root',
  },
  {
    name: '--platform <string>',
    description: 'Either "ios" or "android"'
  },
  {
    name: '--dev [boolean]',
    description: 'If false, warnings are disabled and the bundle is minified',
  },
  {
    name: '--minify [boolean]',
    description: 'Allows overriding whether bundle is minified',
  },
  {
    name: '--bundle-output <string>',
    description: 'File name where to store the resulting bundle',
  },
  {
    name: '--bundle-encoding <string>',
    description: 'Encoding the bundle should be written in',
  },
  {
    name: '--max-workers <number>',
    description: 'Specifies the maximum number of workers',
  },
  {
    name: '--sourcemap-output <string>',
    description: 'File name where to store the sourcemap file',
  },
  {
    name: '--sourcemap-sources-root <string>',
    description: 'Path to make sourcemap sources entries relative to',
  },
  {
    name: '--sourcemap-use-absolute-path',
    description: 'Report SourceMapURL using its full path',
  },
  {
    name: '--assets-dest <string>',
    description: 'Directory name where to store assets',
  },
  {
    name: '--reset-cache',
    description: 'Removes cached files'
  },
  {
    name: '--read-global-cache',
    description: 'Try to fetch transformed JS code from the global cache',
  },
  {
    name: '--config <string>',
    description: 'Path to the CLI configuration file',
  },
];

// 提取命令选项名称
const BUNDLE_COMMAND_OPTIONS = BUNDLE_COMMAND_ARGS.map(
  ({ name }) => name.match(/--[^ \r\n\[<]+/g)[0]
);

// QRN 打包工具自有配置及默认值
const DEFAULT_QRN_OPTIONS = {
  '--output-dir': '.',
  '--bundle-type': 'biz',
  '--qpinfo': '{}',
  '--pltmap-dir': '.',
  '--bumap-dir': '.',
  '--pltmap-output': '.',
  '--bumap-output': '.',
};

// React Native CLI 配置及默认值
const DEFAULT_CLI_OPTIONS = {
  '--platform': 'ios',
  '--config': './qrn.metro.config.js',
  // 动态生成的配置函数
  '--bundle-output': ({ hybridid, platform, bundleType, outputDir }) =>
    path.join(outputDir, `${bundleType}.${hybridid}.${platform}.bundle`),
  '--sourcemap-output': ({ hybridid, platform, bundleType, outputDir }) =>
    path.join(outputDir, `${bundleType}.${hybridid}.${platform}.map`),
  '--assets-dest': ({ outputDir }) => outputDir,
};

// 旧版参数映射
const LEGACY_PARAM_MAP = {
  '--sourcemap-url': '--sourcemap-output',
  '--buildTag': '--build-tag',
  '--bundleType': '--bundle-type',
};

/**
 * 显示帮助信息
 */
function showHelp() {
  const cliDescription = [
    ['--output-dir', '打包产物输出目录 默认值: .'],
    ['--bundle-type', '类型 biz or platform 默认值: biz'],
    ['--buildTag', 'Git Tag 默认值: 无'],
    ...BUNDLE_COMMAND_ARGS.map(({ name, description }) => [name, description])
  ];

  const maxLen = Math.max(...cliDescription.map(([name]) => name.length));

  console.log('');
  console.log('   使用帮助:');
  console.log('       node ./cli.js bundle [options]');
  console.log('       # options 为空等价于');
  console.log('       # node ./cli.js bundle --platform ios --bundle-output biz.demo.ios.bundle --sourcemap-output biz.demo.ios.map --assets-dest . --entry-file index.js');
  console.log('');
  console.log('    options:');

  cliDescription.forEach(([name, description]) => {
    const paddedName = name.padEnd(maxLen);
    console.log(`       ${paddedName} : ${description}`);
  });
}

/**
 * 解析命令行参数
 */
function parseArguments(argv) {
  const start = 3;
  const cmd = argv[start - 1];
  const argvToPass = argv.slice(0, start - 1);
  const qrnOptions = {};
  const cliOptions = {};

  // 检查是否为支持的命令
  if (cmd !== 'bundle' && cmd !== 'ram-bundle') {
    showHelp();
    process.exit(250);
  }

  argvToPass.push(cmd);

  // 解析参数
  for (let i = start; i < argv.length; i++) {
    let name = argv[i].trim();
    let value = argv[i + 1];

    if (!name.startsWith('-')) continue;

    if (value && value.startsWith('-')) {
      value = '';
    } else {
      i++;
    }

    // 处理旧版参数映射
    if (LEGACY_PARAM_MAP[name]) {
      name = LEGACY_PARAM_MAP[name];
    }

    // 分类参数
    if (BUNDLE_COMMAND_OPTIONS.includes(name)) {
      cliOptions[name] = value || DEFAULT_CLI_OPTIONS[name];
    } else {
      qrnOptions[name] = value || DEFAULT_QRN_OPTIONS[name];
    }
  }

  // 设置默认值
  Object.keys(DEFAULT_QRN_OPTIONS).forEach(name => {
    if (!(name in qrnOptions)) {
      qrnOptions[name] = DEFAULT_QRN_OPTIONS[name];
    }
  });

  return { argvToPass, qrnOptions, cliOptions };
}

const cli = module.exports = {
  run: (argv, defaultCli) => {
    console.log(chalk.yellow('⚠️  警告: 使用了旧版调用方式'));
    console.log(chalk.yellow('   推荐使用: npx react-native qrn-bundle'));
    console.log('');

    const { argvToPass, qrnOptions, cliOptions } = parseArguments(argv);

    return duties
      .runBeforeBundle({
        __staticAssets: [],
        entryFile: cliOptions['--entry-file'],
        bundleType: qrnOptions['--bundle-type'],
        buildTag: qrnOptions['--build-tag'],
        outputDir: qrnOptions['--output-dir'],
        qpInfo: qrnOptions['--qpinfo'],
        pltMapDir: qrnOptions['--pltmap-dir'],
        pltMapOutput: qrnOptions['--pltmap-output'],
        buMapDir: qrnOptions['--bumap-dir'],
        buMapOutput: qrnOptions['--bumap-output'],
        isRamBundle: qrnOptions['--isRamBundle'],
      })
      .then((options) => {
        const { bundleType, hybridid, outputDir } = options;
        const platform = cliOptions['--platform'] || DEFAULT_CLI_OPTIONS['--platform'];
        const __staticAssets = options.__staticAssets;

        // 准备 CLI 选项副本
        const cliOptionsCopy = {
          ...cliOptions,
          '--platform': platform,
        };

        // 设置默认值
        Object.keys(DEFAULT_CLI_OPTIONS).forEach(name => {
          if (!(name in cliOptionsCopy)) {
            cliOptionsCopy[name] = DEFAULT_CLI_OPTIONS[name];
          }
        });

        // 确定入口文件
        let entryFile = options.hasReplaceEntry
          ? options.entryFile
          : cliOptionsCopy['--entry-file'];

        if (bundleType === 'platform') {
          entryFile = path.join(__dirname, '..', 'platform.js');
        }

        if (!entryFile) {
          utils.handleError(new Error('entryFile can\'t be empty'));
        }

        cliOptionsCopy['--entry-file'] = entryFile;

        // 构建命令行参数
        const argvToPassCopy = [...argvToPass];

        Object.keys(cliOptionsCopy).forEach(name => {
          if (BUNDLE_COMMAND_OPTIONS.includes(name)) {
            let value = cliOptionsCopy[name];

            if (typeof value === 'function') {
              value = value({ hybridid, bundleType, platform, outputDir });
              cliOptionsCopy[name] = value;
            }

            argvToPassCopy.push(name, value);
          }
        });

        console.log('[LOG] run local-cli/cli:', argvToPassCopy.join(' '));

        return executeBundle(defaultCli, argvToPassCopy, options, cliOptionsCopy, qrnOptions, __staticAssets);
      })
      .then(
        () => console.log(chalk.green('[SUCCESS] 构建完成')),
        utils.handleError
      );
  },
};

/**
 * 执行 bundle 构建
 */
function executeBundle(defaultCli, argvToPassCopy, options, cliOptionsCopy, qrnOptions, __staticAssets) {
  return new Promise((resolve, reject) => {
    // 设置全局环境变量
    global.QRN_PACK_ENV = {
      platform: options.platform || cliOptionsCopy['--platform'],
      bundleType: options.bundleType,
      plt_map_dir: options.pltMapDir,
      plt_map_output: options.pltMapOutput,
      bu_map_dir: options.buMapDir,
      bu_map_output: options.buMapOutput,
      isRamBundle: options.isRamBundle,
    };

    // 保存原始 process.argv
    const originalArgv = process.argv;

    try {
      // 替换 process.argv
      process.argv = argvToPassCopy;

      defaultCli.run(null, () => {
        // 恢复原始 process.argv
        process.argv = originalArgv;

        resolve({
          entryFile: cliOptionsCopy['--entry-file'],
          ...cliOptionsCopy,
          ...qrnOptions,
        });
      });
    } catch (error) {
      // 出错时也要恢复原始 process.argv
      process.argv = originalArgv;
      reject(error);
    }
  })
  .then((bundleOptions) => {
    bundleOptions.qpInfo = qrnOptions['--output-dir'];
    bundleOptions.__staticAssets = __staticAssets;
    return duties.runAfterBundle(bundleOptions);
  });
}
