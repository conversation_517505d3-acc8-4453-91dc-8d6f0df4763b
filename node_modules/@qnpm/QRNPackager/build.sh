 # node -e 'require(require("path").resolve(process.cwd(),"./cli.js")).run()' _placeholder_ bundle --platform ios --dev false --bundle-type platform --tag-name develop

# node ./cli.js bundle --platform ios --dev false --bundle-type platform --buildTag xxxx --output-dir tmp
# node ./cli.js bundle --platform 'ios,android' --dev false --bundle-type platform --buildTag xxxx --output-dir tmp
node ./cli.js bundle --platform ios --dev false --bundleType biz --buildTag xxxx --output-dir tmp
echo $?;